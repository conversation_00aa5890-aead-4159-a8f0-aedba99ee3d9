#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/20
# @File           : role.py
# @IDE            : PyCharm
# @desc           : 角色管理相关API接口

from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from apps.admin.depts import get_admin_db, get_admin_paging, Paging
from apps.admin.services.role import RoleService
from apps.admin.schemas.role import RoleOut, RoleCreate, RoleUpdate
from apps.admin.params.role import RoleParams

app = APIRouter()


@app.get("/roles", summary="获取角色列表")
async def get_roles(
    params: RoleParams = Depends(),
    paging: Paging = get_admin_paging(),
    db: AsyncSession = get_admin_db()
):
    """
    获取角色列表
    """
    service = RoleService(db)
    return await service.get_roles(params, paging)


@app.post("/roles", summary="创建角色")
async def create_role(
    data: RoleCreate,
    db: AsyncSession = get_admin_db()
):
    """
    创建角色
    """
    service = RoleService(db)
    return await service.create_role(data)


@app.get("/roles/{role_id}", summary="获取角色详情")
async def get_role(
    role_id: int,
    db: AsyncSession = get_admin_db()
):
    """
    获取角色详情
    """
    service = RoleService(db)
    return await service.get_role(role_id)


@app.put("/roles/{role_id}", summary="更新角色")
async def update_role(
    role_id: int,
    data: RoleUpdate,
    db: AsyncSession = get_admin_db()
):
    """
    更新角色
    """
    service = RoleService(db)
    return await service.update_role(role_id, data)


@app.delete("/roles/{role_id}", summary="删除角色")
async def delete_role(
    role_id: int,
    db: AsyncSession = get_admin_db()
):
    """
    删除角色
    """
    service = RoleService(db)
    return await service.delete_role(role_id)


@app.post("/roles/{role_id}/menus", summary="分配角色菜单")
async def assign_role_menus(
    role_id: int,
    menu_ids: list[int],
    db: AsyncSession = get_admin_db()
):
    """
    分配角色菜单
    """
    service = RoleService(db)
    return await service.assign_role_menus(role_id, menu_ids)


@app.post("/roles/{role_id}/depts", summary="分配角色部门")
async def assign_role_depts(
    role_id: int,
    dept_ids: list[int],
    db: AsyncSession = get_admin_db()
):
    """
    分配角色部门
    """
    service = RoleService(db)
    return await service.assign_role_depts(role_id, dept_ids)


@app.get("/roles/{role_id}/menus", summary="获取角色菜单")
async def get_role_menus(
    role_id: int,
    db: AsyncSession = get_admin_db()
):
    """
    获取角色菜单
    """
    service = RoleService(db)
    return await service.get_role_menus(role_id)


@app.get("/roles/{role_id}/depts", summary="获取角色部门")
async def get_role_depts(
    role_id: int,
    db: AsyncSession = get_admin_db()
):
    """
    获取角色部门
    """
    service = RoleService(db)
    return await service.get_role_depts(role_id)


@app.post("/roles/batch-delete", summary="批量删除角色")
async def batch_delete_roles(
    role_ids: list[int],
    db: AsyncSession = get_admin_db()
):
    """
    批量删除角色
    """
    service = RoleService(db)
    return await service.batch_delete_roles(role_ids)


@app.post("/roles/{role_id}/enable", summary="启用角色")
async def enable_role(
    role_id: int,
    db: AsyncSession = get_admin_db()
):
    """
    启用角色
    """
    service = RoleService(db)
    return await service.enable_role(role_id)


@app.post("/roles/{role_id}/disable", summary="禁用角色")
async def disable_role(
    role_id: int,
    db: AsyncSession = get_admin_db()
):
    """
    禁用角色
    """
    service = RoleService(db)
    return await service.disable_role(role_id)
