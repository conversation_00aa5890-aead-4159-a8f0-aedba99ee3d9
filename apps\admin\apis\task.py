#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/21
# @File           : task.py
# @IDE            : PyCharm
# @desc           : 任务管理相关API接口

from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from apps.admin.depts import (
    get_admin_db, get_admin_paging, Paging
)
from apps.admin.services.task import TaskService
from apps.admin.schemas.task import (
    TaskOut, TaskCreate, TaskUpdate, TaskListOut, TaskRecordOut,
    TaskExecuteRequest, TaskStatusResponse
)
from apps.admin.params.task import TaskParams, TaskRecordParams
from core.task_scheduler import TaskManager

app = APIRouter()


# ================================ 任务管理相关接口 ================================

@app.get("/tasks", summary="获取任务列表")
async def get_tasks(
    params: TaskParams = Depends(),
    paging: Paging = Depends(get_admin_paging),
    db: AsyncSession = get_admin_db()
):
    """
    获取定时任务列表
    """
    service = TaskService(db)
    return await service.get_tasks(params, paging)


@app.post("/tasks", summary="创建任务")
async def create_task(
    data: TaskCreate,
    db: AsyncSession = get_admin_db()
):
    """
    创建定时任务
    """
    service = TaskService(db)
    return await service.create_task(data)


@app.get("/tasks/{task_id}", summary="获取任务详情")
async def get_task(
    task_id: int,
    db: AsyncSession = get_admin_db()
):
    """
    获取单个任务详情
    """
    service = TaskService(db)
    return await service.get_task(task_id)


@app.put("/tasks/{task_id}", summary="更新任务")
async def update_task(
    task_id: int,
    data: TaskUpdate,
    db: AsyncSession = get_admin_db()
):
    """
    更新定时任务
    """
    service = TaskService(db)
    return await service.update_task(task_id, data)


@app.delete("/tasks/{task_id}", summary="删除任务")
async def delete_task(
    task_id: int,
    db: AsyncSession = get_admin_db()
):
    """
    删除定时任务
    """
    service = TaskService(db)
    return await service.delete_task(task_id)


@app.post("/tasks/{task_id}/execute", summary="立即执行任务")
async def execute_task(
    task_id: int,
    data: TaskExecuteRequest,
    db: AsyncSession = get_admin_db()
):
    """
    立即执行指定任务
    """
    service = TaskService(db)
    return await service.execute_task(task_id, data.args, data.kwargs)


@app.post("/tasks/{task_id}/toggle", summary="切换任务状态")
async def toggle_task_status(
    task_id: int,
    db: AsyncSession = get_admin_db()
):
    """
    启用/禁用任务
    """
    service = TaskService(db)
    return await service.toggle_task_status(task_id)


@app.get("/tasks/{task_id}/status", summary="获取任务状态")
async def get_task_status(
    task_id: int,
    db: AsyncSession = get_admin_db()
):
    """
    获取任务运行状态
    """
    service = TaskService(db)
    return await service.get_task_status(task_id)


@app.get("/task-records", summary="获取任务执行记录")
async def get_task_records(
    params: TaskRecordParams = Depends(),
    paging: Paging = Depends(get_admin_paging),
    db: AsyncSession = get_admin_db()
):
    """
    获取任务执行记录列表
    """
    service = TaskService(db)
    return await service.get_task_records(params, paging)


@app.get("/scheduler/status", summary="获取调度器状态")
async def get_scheduler_status(
    db: AsyncSession = get_admin_db()
):
    """
    获取调度器运行状态
    """
    service = TaskService(db)
    return await service.get_scheduler_status()
