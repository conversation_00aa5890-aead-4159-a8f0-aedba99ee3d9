#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/19
# @File           : system.py
# @IDE            : PyCharm
# @desc           : 系统管理相关的Pydantic模型

from pydantic import BaseModel, ConfigDict, field_validator
from apps.admin.depts import DatetimeStr
from typing import Optional, List, Any


# 字典类型相关模型
class DictTypeBase(BaseModel):
    """字典类型基础模型"""
    dict_name: str
    dict_type: str
    disabled: Optional[bool] = False
    remark: Optional[str] = None


class DictTypeCreate(DictTypeBase):
    """创建字典类型模型"""
    pass


class DictTypeUpdate(BaseModel):
    """更新字典类型模型"""
    dict_name: Optional[str] = None
    dict_type: Optional[str] = None
    disabled: Optional[bool] = None
    remark: Optional[str] = None


class DictTypeOut(DictTypeBase):
    """字典类型输出模型"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    create_datetime: DatetimeStr
    update_datetime: DatetimeStr


# 字典详情相关模型
class DictDetailsBase(BaseModel):
    """字典详情基础模型"""
    label: str
    value: str
    disabled: Optional[bool] = False
    is_default: Optional[bool] = False
    order: int
    dict_type_id: int


class DictDetailsCreate(DictDetailsBase):
    """创建字典详情模型"""
    pass


class DictDetailsUpdate(BaseModel):
    """更新字典详情模型"""
    label: Optional[str] = None
    value: Optional[str] = None
    disabled: Optional[bool] = None
    is_default: Optional[bool] = None
    order: Optional[int] = None
    dict_type_id: Optional[int] = None


class DictDetailsOut(DictDetailsBase):
    """字典详情输出模型"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    create_datetime: DatetimeStr
    update_datetime: DatetimeStr


# 系统配置相关模型
class SettingsBase(BaseModel):
    """系统配置基础模型"""
    config_label: str
    config_key: str
    config_value: Optional[str] = None
    remark: Optional[str] = None
    disabled: Optional[bool] = False
    tab_id: int


class SettingsCreate(SettingsBase):
    """创建系统配置模型"""
    pass


class SettingsUpdate(BaseModel):
    """更新系统配置模型"""
    config_label: Optional[str] = None
    config_key: Optional[str] = None
    config_value: Optional[str] = None
    remark: Optional[str] = None
    disabled: Optional[bool] = None
    tab_id: Optional[int] = None


class SettingsOut(SettingsBase):
    """系统配置输出模型"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    create_datetime: DatetimeStr
    update_datetime: DatetimeStr


# 系统配置标签相关模型
class SettingsTabBase(BaseModel):
    """系统配置标签基础模型"""
    title: str
    classify: str
    tab_label: str
    tab_name: str
    hidden: Optional[bool] = False
    disabled: Optional[bool] = False


class SettingsTabCreate(SettingsTabBase):
    """创建系统配置标签模型"""
    pass


class SettingsTabUpdate(BaseModel):
    """更新系统配置标签模型"""
    title: Optional[str] = None
    classify: Optional[str] = None
    tab_label: Optional[str] = None
    tab_name: Optional[str] = None
    hidden: Optional[bool] = None
    disabled: Optional[bool] = None


class SettingsTabOut(SettingsTabBase):
    """系统配置标签输出模型"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    create_datetime: DatetimeStr
    update_datetime: DatetimeStr


# 定时任务相关模型
class TaskBase(BaseModel):
    """定时任务基础模型"""
    name: str
    group: str
    job_class: str
    exec_strategy: str
    expression: str
    save_log: Optional[bool] = True
    is_active: Optional[bool] = True
    remark: Optional[str] = None


class TaskCreate(TaskBase):
    """创建定时任务模型"""
    pass


class TaskUpdate(BaseModel):
    """更新定时任务模型"""
    name: Optional[str] = None
    group: Optional[str] = None
    job_class: Optional[str] = None
    exec_strategy: Optional[str] = None
    expression: Optional[str] = None
    save_log: Optional[bool] = None
    is_active: Optional[bool] = None
    remark: Optional[str] = None


class TaskOut(TaskBase):
    """定时任务输出模型"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    create_datetime: DatetimeStr
    update_datetime: DatetimeStr


# 文件上传相关模型
class UploadResponse(BaseModel):
    """文件上传响应模型"""
    filename: str
    url: str
    size: int
    content_type: str


class ImageUploadResponse(BaseModel):
    """图片上传响应模型"""
    id: int
    filename: str
    image_url: str
    create_datetime: DatetimeStr


# 系统信息相关模型
class SystemInfoOut(BaseModel):
    """系统信息输出模型"""
    python_version: str
    system_version: str
    cpu_count: int
    memory_total: str
    memory_available: str
    disk_usage: dict


class BaseConfigOut(BaseModel):
    """基础配置输出模型"""
    web_title: Optional[str] = None
    web_desc: Optional[str] = None
    web_logo: Optional[str] = None
    web_ico: Optional[str] = None
