#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/21
# @File           : menu.py
# @IDE            : PyCharm
# @desc           : 菜单管理查询参数

from apps.admin.depts import QueryParams
from typing import Optional


class MenuParams(QueryParams):
    """
    菜单查询参数
    """

    def __init__(
        self,
        title: Optional[str] = None,
        name: Optional[str] = None,
        path: Optional[str] = None,
        menu_type: Optional[str] = None,
        parent_id: Optional[int] = None,
        disabled: Optional[bool] = None,
        hidden: Optional[bool] = None,
        create_datetime_start: Optional[str] = None,
        create_datetime_end: Optional[str] = None,
        params=None
    ):
        super().__init__(params)
        self.title = title
        self.name = name
        self.path = path
        self.menu_type = menu_type
        self.parent_id = parent_id
        self.disabled = disabled
        self.hidden = hidden
        self.create_datetime_start = create_datetime_start
        self.create_datetime_end = create_datetime_end
