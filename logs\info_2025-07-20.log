2025-07-20 21:00:09.828 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://localhost:9000/docs http/1.1' 200utf-8 b'871' 0.0011970996856689453
2025-07-20 21:00:09.916 | ERROR    | core.exception:unicorn_exception_handler:66 - 
Traceback (most recent call last):

  File "C:\Users\<USER>\Documents\mxtt\server\main.py", line 117, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000014C908E5880>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000014C908E5880>
           └ <function get_command at 0x0000014C9283F1A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000014C9283ECA0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 778, in main
    return _main(
           └ <function _main at 0x0000014C9283DBC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000014C94062900>
         │    └ <function MultiCommand.invoke at 0x0000014C926C0900>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000014C9533D280>
           │               │       │       └ <function Command.invoke at 0x0000014C926C02C0>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000014C9533D280>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000014C952FCAE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000014C9533D280>
           │   │      │    └ <function run at 0x0000014C952FCC20>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000014C926B2C00>
           └ <click.core.Context object at 0x0000014C9533D280>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000014C940ABB00>

  File "C:\Users\<USER>\Documents\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000014C926D2FC0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 579, in run
    server.run()
    │      └ <function Server.run at 0x0000014C926F99E0>
    └ <uvicorn.server.Server object at 0x0000014C92EAA930>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000014C926F9A80>
           │       │   └ <uvicorn.server.Server object at 0x0000014C92EAA930>
           │       └ <function run at 0x0000014C8FDEB9C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000014C9532AB20>
           │      └ <function Runner.run at 0x0000014C8FF39440>
           └ <asyncio.runners.Runner object at 0x0000014C9533D400>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000014C8FF36FC0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000014C9533D400>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000014C8FFFACA0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000014C8FF38D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000014C8FDD1080>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000014C95536CA0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C95536700>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000014C954CD640>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000014C9533F1A0>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 68, in __call__
    await self.app(scope, receive, sender)
          │    │   │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000014C95536C00>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C95536700>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <fastapi.middleware.asyncexitstack.AsyncExitStackMiddleware object at 0x0000014C954CC830>
          └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000014C954CD640>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\fastapi\middleware\asyncexitstack.py", line 20, in __call__
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\fastapi\middleware\asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000014C95536C00>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C95536700>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <fastapi.routing.APIRouter object at 0x0000014C954145C0>
          └ <fastapi.middleware.asyncexitstack.AsyncExitStackMiddleware object at 0x0000014C954CC830>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 718, in __call__
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000014C95536C00>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C95536700>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000014C924FAB60>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000014C95414650>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 443, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000014C95536C00>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C95536700>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000014C95414650>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000014C95414650>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 103, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'swagger_ui\\swagger-ui.css'
                     │    └ <function StaticFiles.get_response at 0x0000014C9274F920>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000014C95414650>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 160, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException
2025-07-20 21:00:09.935 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://localhost:9000/media/swagger_ui/swagger-ui.css http/1.1' 404utf-8 b'34' 0.023877620697021484
2025-07-20 21:00:09.941 | ERROR    | core.exception:unicorn_exception_handler:66 - 
Traceback (most recent call last):

  File "C:\Users\<USER>\Documents\mxtt\server\main.py", line 117, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000014C908E5880>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000014C908E5880>
           └ <function get_command at 0x0000014C9283F1A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000014C9283ECA0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 778, in main
    return _main(
           └ <function _main at 0x0000014C9283DBC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000014C94062900>
         │    └ <function MultiCommand.invoke at 0x0000014C926C0900>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000014C9533D280>
           │               │       │       └ <function Command.invoke at 0x0000014C926C02C0>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000014C9533D280>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000014C952FCAE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000014C9533D280>
           │   │      │    └ <function run at 0x0000014C952FCC20>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000014C926B2C00>
           └ <click.core.Context object at 0x0000014C9533D280>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000014C940ABB00>

  File "C:\Users\<USER>\Documents\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000014C926D2FC0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 579, in run
    server.run()
    │      └ <function Server.run at 0x0000014C926F99E0>
    └ <uvicorn.server.Server object at 0x0000014C92EAA930>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000014C926F9A80>
           │       │   └ <uvicorn.server.Server object at 0x0000014C92EAA930>
           │       └ <function run at 0x0000014C8FDEB9C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000014C9532AB20>
           │      └ <function Runner.run at 0x0000014C8FF39440>
           └ <asyncio.runners.Runner object at 0x0000014C9533D400>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000014C8FF36FC0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000014C9533D400>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000014C8FFFACA0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000014C8FF38D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000014C8FDD1080>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000014C95628F40>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C95628E00>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000014C954CD640>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000014C9533F1A0>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 68, in __call__
    await self.app(scope, receive, sender)
          │    │   │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000014C95628400>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C95628E00>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <fastapi.middleware.asyncexitstack.AsyncExitStackMiddleware object at 0x0000014C954CC830>
          └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000014C954CD640>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\fastapi\middleware\asyncexitstack.py", line 20, in __call__
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\fastapi\middleware\asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000014C95628400>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C95628E00>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <fastapi.routing.APIRouter object at 0x0000014C954145C0>
          └ <fastapi.middleware.asyncexitstack.AsyncExitStackMiddleware object at 0x0000014C954CC830>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 718, in __call__
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000014C95628400>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C95628E00>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000014C924FAB60>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000014C95414650>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 443, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000014C95628400>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C95628E00>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000014C95414650>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000014C95414650>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 103, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'swagger_ui\\swagger-ui-bundle.js'
                     │    └ <function StaticFiles.get_response at 0x0000014C9274F920>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000014C95414650>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 160, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException
2025-07-20 21:00:09.950 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://localhost:9000/media/swagger_ui/swagger-ui-bundle.js http/1.1' 404utf-8 b'34' 0.014132499694824219
2025-07-20 21:00:18.435 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://localhost:9000/docs http/1.1' 200utf-8 b'871' 0.0
2025-07-20 21:00:18.485 | ERROR    | core.exception:unicorn_exception_handler:66 - 
Traceback (most recent call last):

  File "C:\Users\<USER>\Documents\mxtt\server\main.py", line 117, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000014C908E5880>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000014C908E5880>
           └ <function get_command at 0x0000014C9283F1A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000014C9283ECA0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 778, in main
    return _main(
           └ <function _main at 0x0000014C9283DBC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000014C94062900>
         │    └ <function MultiCommand.invoke at 0x0000014C926C0900>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000014C9533D280>
           │               │       │       └ <function Command.invoke at 0x0000014C926C02C0>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000014C9533D280>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000014C952FCAE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000014C9533D280>
           │   │      │    └ <function run at 0x0000014C952FCC20>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000014C926B2C00>
           └ <click.core.Context object at 0x0000014C9533D280>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000014C940ABB00>

  File "C:\Users\<USER>\Documents\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000014C926D2FC0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 579, in run
    server.run()
    │      └ <function Server.run at 0x0000014C926F99E0>
    └ <uvicorn.server.Server object at 0x0000014C92EAA930>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000014C926F9A80>
           │       │   └ <uvicorn.server.Server object at 0x0000014C92EAA930>
           │       └ <function run at 0x0000014C8FDEB9C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000014C9532AB20>
           │      └ <function Runner.run at 0x0000014C8FF39440>
           └ <asyncio.runners.Runner object at 0x0000014C9533D400>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000014C8FF36FC0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000014C9533D400>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000014C8FFFACA0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000014C8FF38D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000014C8FDD1080>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000014C95629300>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C95629C60>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000014C954CD640>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000014C9533F1A0>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 68, in __call__
    await self.app(scope, receive, sender)
          │    │   │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000014C9562A7A0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C95629C60>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <fastapi.middleware.asyncexitstack.AsyncExitStackMiddleware object at 0x0000014C954CC830>
          └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000014C954CD640>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\fastapi\middleware\asyncexitstack.py", line 20, in __call__
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\fastapi\middleware\asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000014C9562A7A0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C95629C60>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <fastapi.routing.APIRouter object at 0x0000014C954145C0>
          └ <fastapi.middleware.asyncexitstack.AsyncExitStackMiddleware object at 0x0000014C954CC830>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 718, in __call__
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000014C9562A7A0>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C95629C60>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000014C924FAB60>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000014C95414650>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 443, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000014C9562A7A0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C95629C60>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000014C95414650>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000014C95414650>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 103, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'swagger_ui\\swagger-ui.css'
                     │    └ <function StaticFiles.get_response at 0x0000014C9274F920>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000014C95414650>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 160, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException
2025-07-20 21:00:18.494 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://localhost:9000/media/swagger_ui/swagger-ui.css http/1.1' 404utf-8 b'34' 0.011786937713623047
2025-07-20 21:00:18.502 | ERROR    | core.exception:unicorn_exception_handler:66 - 
Traceback (most recent call last):

  File "C:\Users\<USER>\Documents\mxtt\server\main.py", line 117, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000014C908E5880>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000014C908E5880>
           └ <function get_command at 0x0000014C9283F1A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000014C9283ECA0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 778, in main
    return _main(
           └ <function _main at 0x0000014C9283DBC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000014C94062900>
         │    └ <function MultiCommand.invoke at 0x0000014C926C0900>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000014C9533D280>
           │               │       │       └ <function Command.invoke at 0x0000014C926C02C0>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000014C9533D280>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000014C952FCAE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000014C9533D280>
           │   │      │    └ <function run at 0x0000014C952FCC20>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000014C926B2C00>
           └ <click.core.Context object at 0x0000014C9533D280>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000014C940ABB00>

  File "C:\Users\<USER>\Documents\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000014C926D2FC0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 579, in run
    server.run()
    │      └ <function Server.run at 0x0000014C926F99E0>
    └ <uvicorn.server.Server object at 0x0000014C92EAA930>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000014C926F9A80>
           │       │   └ <uvicorn.server.Server object at 0x0000014C92EAA930>
           │       └ <function run at 0x0000014C8FDEB9C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000014C9532AB20>
           │      └ <function Runner.run at 0x0000014C8FF39440>
           └ <asyncio.runners.Runner object at 0x0000014C9533D400>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000014C8FF36FC0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000014C9533D400>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000014C8FFFACA0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000014C8FF38D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000014C8FDD1080>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000014C95536200>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C9548AA20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000014C954CD640>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000014C9533F1A0>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 68, in __call__
    await self.app(scope, receive, sender)
          │    │   │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000014C95536E80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C9548AA20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <fastapi.middleware.asyncexitstack.AsyncExitStackMiddleware object at 0x0000014C954CC830>
          └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000014C954CD640>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\fastapi\middleware\asyncexitstack.py", line 20, in __call__
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\fastapi\middleware\asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000014C95536E80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C9548AA20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <fastapi.routing.APIRouter object at 0x0000014C954145C0>
          └ <fastapi.middleware.asyncexitstack.AsyncExitStackMiddleware object at 0x0000014C954CC830>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 718, in __call__
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000014C95536E80>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C9548AA20>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000014C924FAB60>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000014C95414650>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 443, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000014C95536E80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C9548AA20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000014C95414650>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000014C95414650>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 103, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'swagger_ui\\swagger-ui-bundle.js'
                     │    └ <function StaticFiles.get_response at 0x0000014C9274F920>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000014C95414650>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 160, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException
2025-07-20 21:00:18.507 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://localhost:9000/media/swagger_ui/swagger-ui-bundle.js http/1.1' 404utf-8 b'34' 0.00903630256652832
2025-07-20 21:00:37.201 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://localhost:9000/docs http/1.1' 200utf-8 b'871' 0.0
2025-07-20 21:00:37.256 | ERROR    | core.exception:unicorn_exception_handler:66 - 
Traceback (most recent call last):

  File "C:\Users\<USER>\Documents\mxtt\server\main.py", line 117, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000014C908E5880>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000014C908E5880>
           └ <function get_command at 0x0000014C9283F1A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000014C9283ECA0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 778, in main
    return _main(
           └ <function _main at 0x0000014C9283DBC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000014C94062900>
         │    └ <function MultiCommand.invoke at 0x0000014C926C0900>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000014C9533D280>
           │               │       │       └ <function Command.invoke at 0x0000014C926C02C0>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000014C9533D280>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000014C952FCAE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000014C9533D280>
           │   │      │    └ <function run at 0x0000014C952FCC20>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000014C926B2C00>
           └ <click.core.Context object at 0x0000014C9533D280>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000014C940ABB00>

  File "C:\Users\<USER>\Documents\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000014C926D2FC0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 579, in run
    server.run()
    │      └ <function Server.run at 0x0000014C926F99E0>
    └ <uvicorn.server.Server object at 0x0000014C92EAA930>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000014C926F9A80>
           │       │   └ <uvicorn.server.Server object at 0x0000014C92EAA930>
           │       └ <function run at 0x0000014C8FDEB9C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000014C9532AB20>
           │      └ <function Runner.run at 0x0000014C8FF39440>
           └ <asyncio.runners.Runner object at 0x0000014C9533D400>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000014C8FF36FC0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000014C9533D400>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000014C8FFFACA0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000014C8FF38D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000014C8FDD1080>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000014C956284A0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C9562AC00>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000014C954CD640>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000014C9533F1A0>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 68, in __call__
    await self.app(scope, receive, sender)
          │    │   │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000014C9562AA20>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C9562AC00>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <fastapi.middleware.asyncexitstack.AsyncExitStackMiddleware object at 0x0000014C954CC830>
          └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000014C954CD640>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\fastapi\middleware\asyncexitstack.py", line 20, in __call__
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\fastapi\middleware\asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000014C9562AA20>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C9562AC00>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <fastapi.routing.APIRouter object at 0x0000014C954145C0>
          └ <fastapi.middleware.asyncexitstack.AsyncExitStackMiddleware object at 0x0000014C954CC830>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 718, in __call__
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000014C9562AA20>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C9562AC00>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000014C924FAB60>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000014C95414650>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 443, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000014C9562AA20>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C9562AC00>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000014C95414650>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000014C95414650>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 103, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'swagger_ui\\swagger-ui.css'
                     │    └ <function StaticFiles.get_response at 0x0000014C9274F920>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000014C95414650>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 160, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException
2025-07-20 21:00:37.268 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://localhost:9000/media/swagger_ui/swagger-ui.css http/1.1' 404utf-8 b'34' 0.019628524780273438
2025-07-20 21:00:37.283 | ERROR    | core.exception:unicorn_exception_handler:66 - 
Traceback (most recent call last):

  File "C:\Users\<USER>\Documents\mxtt\server\main.py", line 117, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000014C908E5880>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000014C908E5880>
           └ <function get_command at 0x0000014C9283F1A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000014C9283ECA0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 778, in main
    return _main(
           └ <function _main at 0x0000014C9283DBC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000014C94062900>
         │    └ <function MultiCommand.invoke at 0x0000014C926C0900>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000014C9533D280>
           │               │       │       └ <function Command.invoke at 0x0000014C926C02C0>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000014C9533D280>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000014C952FCAE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000014C9533D280>
           │   │      │    └ <function run at 0x0000014C952FCC20>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000014C926B2C00>
           └ <click.core.Context object at 0x0000014C9533D280>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000014C940ABB00>

  File "C:\Users\<USER>\Documents\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000014C926D2FC0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 579, in run
    server.run()
    │      └ <function Server.run at 0x0000014C926F99E0>
    └ <uvicorn.server.Server object at 0x0000014C92EAA930>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000014C926F9A80>
           │       │   └ <uvicorn.server.Server object at 0x0000014C92EAA930>
           │       └ <function run at 0x0000014C8FDEB9C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000014C9532AB20>
           │      └ <function Runner.run at 0x0000014C8FF39440>
           └ <asyncio.runners.Runner object at 0x0000014C9533D400>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000014C8FF36FC0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000014C9533D400>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000014C8FFFACA0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000014C8FF38D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000014C8FDD1080>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000014C95629C60>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C95628220>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000014C954CD640>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000014C9533F1A0>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 68, in __call__
    await self.app(scope, receive, sender)
          │    │   │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000014C95628180>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C95628220>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <fastapi.middleware.asyncexitstack.AsyncExitStackMiddleware object at 0x0000014C954CC830>
          └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000014C954CD640>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\fastapi\middleware\asyncexitstack.py", line 20, in __call__
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\fastapi\middleware\asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000014C95628180>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C95628220>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <fastapi.routing.APIRouter object at 0x0000014C954145C0>
          └ <fastapi.middleware.asyncexitstack.AsyncExitStackMiddleware object at 0x0000014C954CC830>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 718, in __call__
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000014C95628180>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C95628220>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000014C924FAB60>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000014C95414650>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 443, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000014C95628180>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C95628220>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000014C95414650>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000014C95414650>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 103, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'swagger_ui\\swagger-ui-bundle.js'
                     │    └ <function StaticFiles.get_response at 0x0000014C9274F920>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000014C95414650>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 160, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException
2025-07-20 21:00:37.293 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://localhost:9000/media/swagger_ui/swagger-ui-bundle.js http/1.1' 404utf-8 b'34' 0.013321399688720703
2025-07-20 21:01:25.779 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://localhost:9000/redoc http/1.1' 200utf-8 b'857' 0.0006804466247558594
2025-07-20 21:01:25.851 | ERROR    | core.exception:unicorn_exception_handler:66 - 
Traceback (most recent call last):

  File "C:\Users\<USER>\Documents\mxtt\server\main.py", line 117, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000014C908E5880>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000014C908E5880>
           └ <function get_command at 0x0000014C9283F1A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000014C9283ECA0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 778, in main
    return _main(
           └ <function _main at 0x0000014C9283DBC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000014C94062900>
         │    └ <function MultiCommand.invoke at 0x0000014C926C0900>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000014C9533D280>
           │               │       │       └ <function Command.invoke at 0x0000014C926C02C0>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000014C9533D280>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000014C952FCAE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000014C9533D280>
           │   │      │    └ <function run at 0x0000014C952FCC20>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000014C926B2C00>
           └ <click.core.Context object at 0x0000014C9533D280>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000014C940ABB00>

  File "C:\Users\<USER>\Documents\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000014C926D2FC0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 579, in run
    server.run()
    │      └ <function Server.run at 0x0000014C926F99E0>
    └ <uvicorn.server.Server object at 0x0000014C92EAA930>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000014C926F9A80>
           │       │   └ <uvicorn.server.Server object at 0x0000014C92EAA930>
           │       └ <function run at 0x0000014C8FDEB9C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000014C9532AB20>
           │      └ <function Runner.run at 0x0000014C8FF39440>
           └ <asyncio.runners.Runner object at 0x0000014C9533D400>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000014C8FF36FC0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000014C9533D400>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000014C8FFFACA0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000014C8FF38D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000014C8FDD1080>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000014C9562B100>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C95628F40>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000014C954CD640>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000014C9533F1A0>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 68, in __call__
    await self.app(scope, receive, sender)
          │    │   │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000014C9562B380>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C95628F40>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <fastapi.middleware.asyncexitstack.AsyncExitStackMiddleware object at 0x0000014C954CC830>
          └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000014C954CD640>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\fastapi\middleware\asyncexitstack.py", line 20, in __call__
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\fastapi\middleware\asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000014C9562B380>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C95628F40>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <fastapi.routing.APIRouter object at 0x0000014C954145C0>
          └ <fastapi.middleware.asyncexitstack.AsyncExitStackMiddleware object at 0x0000014C954CC830>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 718, in __call__
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000014C9562B380>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C95628F40>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000014C924FAB60>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000014C95414650>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 443, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000014C9562B380>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C95628F40>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000014C95414650>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000014C95414650>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 103, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'redoc_ui\\redoc.standalone.js'
                     │    └ <function StaticFiles.get_response at 0x0000014C9274F920>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000014C95414650>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 160, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException
2025-07-20 21:01:25.858 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://localhost:9000/media/redoc_ui/redoc.standalone.js http/1.1' 404utf-8 b'34' 0.008800506591796875
2025-07-20 21:16:25.108 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://localhost:9000/redoc http/1.1' 200utf-8 b'857' 0.0015506744384765625
2025-07-20 21:16:25.145 | ERROR    | core.exception:unicorn_exception_handler:66 - 
Traceback (most recent call last):

  File "C:\Users\<USER>\Documents\mxtt\server\main.py", line 117, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000014C908E5880>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000014C908E5880>
           └ <function get_command at 0x0000014C9283F1A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000014C9283ECA0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 778, in main
    return _main(
           └ <function _main at 0x0000014C9283DBC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000014C94062900>
         │    └ <function MultiCommand.invoke at 0x0000014C926C0900>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000014C9533D280>
           │               │       │       └ <function Command.invoke at 0x0000014C926C02C0>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000014C9533D280>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000014C952FCAE0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000014C9533D280>
           │   │      │    └ <function run at 0x0000014C952FCC20>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000014C926B2C00>
           └ <click.core.Context object at 0x0000014C9533D280>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000014C940ABB00>

  File "C:\Users\<USER>\Documents\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000014C926D2FC0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 579, in run
    server.run()
    │      └ <function Server.run at 0x0000014C926F99E0>
    └ <uvicorn.server.Server object at 0x0000014C92EAA930>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 65, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000014C926F9A80>
           │       │   └ <uvicorn.server.Server object at 0x0000014C92EAA930>
           │       └ <function run at 0x0000014C8FDEB9C0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000014C9532AB20>
           │      └ <function Runner.run at 0x0000014C8FF39440>
           └ <asyncio.runners.Runner object at 0x0000014C9533D400>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000014C8FF36FC0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000014C9533D400>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000014C8FFFACA0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000014C8FF38D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000014C8FDD1080>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 70, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000014C95629BC0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C9562A840>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000014C954CD640>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000014C9533F1A0>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 68, in __call__
    await self.app(scope, receive, sender)
          │    │   │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000014C9562A480>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C9562A840>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <fastapi.middleware.asyncexitstack.AsyncExitStackMiddleware object at 0x0000014C954CC830>
          └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000014C954CD640>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\fastapi\middleware\asyncexitstack.py", line 20, in __call__
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\fastapi\middleware\asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000014C9562A480>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C9562A840>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <fastapi.routing.APIRouter object at 0x0000014C954145C0>
          └ <fastapi.middleware.asyncexitstack.AsyncExitStackMiddleware object at 0x0000014C954CC830>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 718, in __call__
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000014C9562A480>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C9562A840>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000014C924FAB60>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000014C95414650>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 443, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ExceptionMiddleware.__call__.<locals>.sender at 0x0000014C9562A480>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014C9562A840>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000014C95414650>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000014C95414650>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 103, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.4'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'redoc_ui\\redoc.standalone.js'
                     │    └ <function StaticFiles.get_response at 0x0000014C9274F920>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000014C95414650>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 160, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException
2025-07-20 21:16:25.159 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://localhost:9000/media/redoc_ui/redoc.standalone.js http/1.1' 404utf-8 b'34' 0.02701401710510254
2025-07-20 21:16:52.398 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://localhost:9000/redoc http/1.1' 200utf-8 b'890' 0.0
2025-07-20 21:16:53.552 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://localhost:9000/openapi.json http/1.1' 200utf-8 b'51780' 0.060503244400024414
2025-07-20 21:17:04.182 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://localhost:9000/docs http/1.1' 200utf-8 b'937' 0.0
2025-07-20 21:17:04.650 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://localhost:9000/openapi.json http/1.1' 200utf-8 b'51780' 0.0011866092681884766
