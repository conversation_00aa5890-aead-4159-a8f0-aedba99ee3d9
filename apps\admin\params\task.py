# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/20
# @File           : task.py
# @IDE            : PyCharm
# @desc           : 任务参数模型

from typing import Optional
from pydantic import BaseModel, Field
from core.dependencies import QueryParams


class TaskParams(QueryParams):
    """
    任务查询参数
    """
    name: Optional[str] = Field(None, description="任务名称")
    job_class: Optional[str] = Field(None, description="任务类路径")
    exec_strategy: Optional[str] = Field(None, description="执行策略")
    group: Optional[str] = Field(None, description="任务分组")
    is_active: Optional[bool] = Field(None, description="是否启用")


class TaskRecordParams(QueryParams):
    """
    任务执行记录查询参数
    """
    job_id: Optional[str] = Field(None, description="任务ID")
    name: Optional[str] = Field(None, description="任务名称")
    group: Optional[str] = Field(None, description="任务分组")
    exec_strategy: Optional[str] = Field(None, description="执行策略")
    start_time_start: Optional[str] = Field(None, description="开始时间-起始")
    start_time_end: Optional[str] = Field(None, description="开始时间-结束")
    has_exception: Optional[bool] = Field(None, description="是否有异常")
