#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/19
# @File           : record.py
# @IDE            : PyCharm
# @desc           : 记录管理相关API接口

from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from motor.motor_asyncio import AsyncIOMotorDatabase
from apps.admin.depts import get_admin_db, get_admin_mongo_db, get_admin_redis, get_admin_paging, get_admin_id_list, Paging, IdList, SuccessResponse
from apps.admin.services.record import RecordService
from apps.admin.schemas.record import (
    LoginRecordOut, SMSRecordOut, OperationRecordOut
)
from apps.admin.params.record import (
    LoginRecordParams, SMSRecordParams, OperationRecordParams
)

app = APIRouter()


###########################################################
#    日志管理
###########################################################
@app.get("/logins", summary="获取登录日志列表")
async def get_login_records(
    params: LoginRecordParams = Depends(),
    paging: Paging = get_admin_paging(),
    db: AsyncSession = get_admin_db()
):
    """
    获取登录日志列表
    """
    service = RecordService(db)
    return await service.get_login_records(params, paging)


@app.get("/operations", summary="获取操作日志列表")
async def get_operation_records(
    params: OperationRecordParams = Depends(),
    paging: Paging = get_admin_paging(),
    mongo_db: AsyncIOMotorDatabase = get_admin_mongo_db()
):
    """
    获取操作日志列表
    """
    service = RecordService(None, mongo_db)
    return await service.get_operation_records(params, paging)


@app.get("/sms/send/list", summary="获取短信发送列表")
async def get_sms_records(
    params: SMSRecordParams = Depends(),
    paging: Paging = get_admin_paging(),
    db: AsyncSession = get_admin_db()
):
    """
    获取短信发送列表
    """
    service = RecordService(db)
    return await service.get_sms_records(params, paging)


###########################################################
#    日志分析
###########################################################
@app.get("/analysis/user/login/distribute", summary="获取用户登录分布情况列表")
async def get_user_login_distribute(
    db: AsyncSession = get_admin_db()
):
    """
    获取用户登录分布情况列表
    """
    service = RecordService(db)
    return await service.get_user_login_distribute()


@app.get("/analysis/user/login/latest", summary="获取最新用户登录列表")
async def get_user_login_latest(
    db: AsyncSession = get_admin_db()
):
    """
    获取最新用户登录列表
    """
    service = RecordService(db)
    return await service.get_user_login_latest()


@app.get("/analysis/user/login/count", summary="获取用户登录统计")
async def get_user_login_count(
    db: AsyncSession = get_admin_db()
):
    """
    获取用户登录统计
    """
    service = RecordService(db)
    return await service.get_user_login_count()
