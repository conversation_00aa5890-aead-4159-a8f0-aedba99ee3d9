#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2022/7/7 13:41
# @File           : dept.py
# @IDE            : PyCharm
# @desc           : 部门模型

from sqlalchemy.orm import Mapped, mapped_column
from datetime import datetime
from sqlalchemy import DateTime, Integer, func, Boolean
from models.base import BaseModel
from sqlalchemy import String, Boolean, Integer


class AdminDept(BaseModel):
    __tablename__ = "admin_auth_dept"
    # 基础字段
    id: Mapped[int] = mapped_column(Integer, primary_key=True, comment='主键ID')
    create_datetime: Mapped[datetime] = mapped_column(DateTime, server_default=func.now(), comment='创建时间')
    update_datetime: Mapped[datetime] = mapped_column(
        DateTime,
        server_default=func.now(),
        onupdate=func.now(),
        comment='更新时间'
    )
    delete_datetime: Mapped[datetime | None] = mapped_column(DateTime, nullable=True, comment='删除时间')
    is_delete: Mapped[bool] = mapped_column(<PERSON>olean, default=False, comment="是否软删除")

    __table_args__ = ({'comment': '部门表'})

    name: Mapped[str] = mapped_column(String(50), comment="部门名称")
    dept_key: Mapped[str] = mapped_column(String(50), comment="部门标识")
    disabled: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否禁用")
    order: Mapped[int | None] = mapped_column(Integer, comment="排序")
    desc: Mapped[str | None] = mapped_column(String(255), comment="描述")
    owner: Mapped[str | None] = mapped_column(String(50), comment="负责人")
    phone: Mapped[str | None] = mapped_column(String(11), comment="联系电话")
    email: Mapped[str | None] = mapped_column(String(50), comment="邮箱")
    parent_id: Mapped[int | None] = mapped_column(Integer, comment="父部门ID")
