#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/21
# @File           : menu.py
# @IDE            : PyCharm
# @desc           : 菜单管理相关数据模型

from pydantic import BaseModel, ConfigDict, field_validator
from typing import Optional, List
from core.data_types import DatetimeStr


class MenuCreate(BaseModel):
    """菜单创建模型"""
    title: str
    name: str
    path: str
    component: Optional[str] = None
    redirect: Optional[str] = None
    menu_type: str  # directory, menu, button
    icon: Optional[str] = None
    order: Optional[int] = 0
    parent_id: Optional[int] = None
    disabled: bool = False
    hidden: bool = False
    always_show: bool = False
    breadcrumb: bool = True
    no_cache: bool = False
    affix: bool = False
    perms: Optional[str] = None  # 权限标识

    @field_validator('title')
    @classmethod
    def validate_title(cls, v: str) -> str:
        if not v or len(v.strip()) == 0:
            raise ValueError('菜单标题不能为空')
        return v.strip()

    @field_validator('name')
    @classmethod
    def validate_name(cls, v: str) -> str:
        if not v or len(v.strip()) == 0:
            raise ValueError('菜单名称不能为空')
        return v.strip()

    @field_validator('menu_type')
    @classmethod
    def validate_menu_type(cls, v: str) -> str:
        if v not in ['directory', 'menu', 'button']:
            raise ValueError('菜单类型必须是directory、menu或button')
        return v


class MenuUpdate(BaseModel):
    """菜单更新模型"""
    title: Optional[str] = None
    name: Optional[str] = None
    path: Optional[str] = None
    component: Optional[str] = None
    redirect: Optional[str] = None
    menu_type: Optional[str] = None
    icon: Optional[str] = None
    order: Optional[int] = None
    parent_id: Optional[int] = None
    disabled: Optional[bool] = None
    hidden: Optional[bool] = None
    always_show: Optional[bool] = None
    breadcrumb: Optional[bool] = None
    no_cache: Optional[bool] = None
    affix: Optional[bool] = None
    perms: Optional[str] = None

    @field_validator('menu_type')
    @classmethod
    def validate_menu_type(cls, v: Optional[str]) -> Optional[str]:
        if v is not None and v not in ['directory', 'menu', 'button']:
            raise ValueError('菜单类型必须是directory、menu或button')
        return v


class MenuOut(BaseModel):
    """菜单输出模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    title: str
    name: str
    path: str
    component: Optional[str] = None
    redirect: Optional[str] = None
    menu_type: str
    icon: Optional[str] = None
    order: Optional[int] = None
    parent_id: Optional[int] = None
    disabled: bool
    hidden: bool
    always_show: bool
    breadcrumb: bool
    no_cache: bool
    affix: bool
    perms: Optional[str] = None
    create_datetime: DatetimeStr
    update_datetime: Optional[DatetimeStr] = None

    # 子菜单
    children: Optional[List["MenuOut"]] = []


class MenuTreeOut(BaseModel):
    """菜单树输出模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    title: str
    name: str
    path: str
    component: Optional[str] = None
    redirect: Optional[str] = None
    menu_type: str
    icon: Optional[str] = None
    order: Optional[int] = None
    parent_id: Optional[int] = None
    disabled: bool
    hidden: bool
    always_show: bool
    breadcrumb: bool
    no_cache: bool
    affix: bool
    perms: Optional[str] = None

    # 子菜单
    children: Optional[List["MenuTreeOut"]] = []


class MenuOptionOut(BaseModel):
    """菜单选项输出模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    title: str
    name: str
    parent_id: Optional[int] = None
    disabled: bool


class MenuListOut(BaseModel):
    """菜单列表输出模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    title: str
    name: str
    path: str
    menu_type: str
    icon: Optional[str] = None
    order: Optional[int] = None
    parent_id: Optional[int] = None
    disabled: bool
    hidden: bool
    create_datetime: DatetimeStr


class MenuPermissionOut(BaseModel):
    """菜单权限输出模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    title: str
    name: str
    path: str
    menu_type: str
    perms: Optional[str] = None
    parent_id: Optional[int] = None
    disabled: bool

    # 子菜单权限
    children: Optional[List["MenuPermissionOut"]] = []


class RouterOut(BaseModel):
    """路由输出模型（前端路由格式）"""
    model_config = ConfigDict(from_attributes=True)

    path: str
    name: str
    component: Optional[str] = None
    redirect: Optional[str] = None
    meta: dict
    children: Optional[List["RouterOut"]] = []

    @classmethod
    def from_menu(cls, menu) -> "RouterOut":
        """从菜单模型转换为路由模型"""
        meta = {
            "title": menu.title,
            "icon": menu.icon,
            "hidden": menu.hidden,
            "always_show": menu.always_show,
            "breadcrumb": menu.breadcrumb,
            "no_cache": menu.no_cache,
            "affix": menu.affix,
        }
        
        return cls(
            path=menu.path,
            name=menu.name,
            component=menu.component,
            redirect=menu.redirect,
            meta=meta,
            children=[]
        )


class MenuBatchRequest(BaseModel):
    """菜单批量操作请求模型"""
    menu_ids: List[int]

    @field_validator('menu_ids')
    @classmethod
    def validate_menu_ids(cls, v: List[int]) -> List[int]:
        if not v or len(v) == 0:
            raise ValueError('菜单ID列表不能为空')
        return v


class MenuMoveRequest(BaseModel):
    """菜单移动请求模型"""
    target_parent_id: Optional[int] = None
    target_order: Optional[int] = None
