#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/21
# @File           : menu.py
# @IDE            : PyCharm
# @desc           : 菜单管理相关API接口

from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
from apps.admin.depts import get_admin_db, get_admin_paging, Paging
from apps.admin.services.menu import MenuService
from apps.admin.schemas.menu import (
    MenuOut, MenuCreate, MenuUpdate, MenuBatchRequest
)
from apps.admin.params.menu import MenuParams
from typing import List, Optional

app = APIRouter()


@app.get("/menus", summary="获取菜单列表")
async def get_menus(
    params: MenuParams = Depends(),
    paging: Paging = Depends(get_admin_paging),
    db: AsyncSession = get_admin_db()
):
    """
    获取菜单列表
    """
    service = MenuService(db)
    return await service.get_menus(params, paging)


@app.post("/menus", summary="创建菜单")
async def create_menu(
    data: MenuCreate,
    db: AsyncSession = get_admin_db()
):
    """
    创建菜单
    """
    service = MenuService(db)
    return await service.create_menu(data)


@app.get("/menus/{menu_id}", summary="获取菜单详情")
async def get_menu(
    menu_id: int,
    db: AsyncSession = get_admin_db()
):
    """
    获取菜单详情
    """
    service = MenuService(db)
    return await service.get_menu(menu_id)


@app.put("/menus/{menu_id}", summary="更新菜单")
async def update_menu(
    menu_id: int,
    data: MenuUpdate,
    db: AsyncSession = get_admin_db()
):
    """
    更新菜单
    """
    service = MenuService(db)
    return await service.update_menu(menu_id, data)


@app.delete("/menus/{menu_id}", summary="删除菜单")
async def delete_menu(
    menu_id: int,
    db: AsyncSession = get_admin_db()
):
    """
    删除菜单
    """
    service = MenuService(db)
    return await service.delete_menus([menu_id])


@app.post("/menus/batch-delete", summary="批量删除菜单")
async def batch_delete_menus(
    data: MenuBatchRequest,
    db: AsyncSession = get_admin_db()
):
    """
    批量删除菜单
    """
    service = MenuService(db)
    return await service.delete_menus(data.menu_ids)


@app.get("/menus/tree", summary="获取菜单树")
async def get_menu_tree(
    include_disabled: bool = Query(False, description="是否包含禁用的菜单"),
    db: AsyncSession = get_admin_db()
):
    """
    获取菜单树结构
    """
    service = MenuService(db)
    return await service.get_menu_tree(include_disabled)


@app.get("/menus/options", summary="获取菜单选项")
async def get_menu_options(
    exclude_id: Optional[int] = Query(None, description="排除的菜单ID"),
    db: AsyncSession = get_admin_db()
):
    """
    获取菜单选项（用于父菜单选择）
    """
    service = MenuService(db)
    return await service.get_menu_options(exclude_id)


@app.get("/menus/routers/{user_id}", summary="获取用户路由")
async def get_user_routers(
    user_id: int,
    db: AsyncSession = get_admin_db()
):
    """
    获取用户路由（前端路由格式）
    """
    service = MenuService(db)
    return await service.get_user_routers(user_id)


@app.post("/menus/{menu_id}/enable", summary="启用菜单")
async def enable_menu(
    menu_id: int,
    db: AsyncSession = get_admin_db()
):
    """
    启用菜单
    """
    service = MenuService(db)
    return await service.update_menu(menu_id, MenuUpdate(disabled=False))


@app.post("/menus/{menu_id}/disable", summary="禁用菜单")
async def disable_menu(
    menu_id: int,
    db: AsyncSession = get_admin_db()
):
    """
    禁用菜单
    """
    service = MenuService(db)
    return await service.update_menu(menu_id, MenuUpdate(disabled=True))
