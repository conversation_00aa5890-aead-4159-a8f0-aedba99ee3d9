#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/19
# @File           : help.py
# @IDE            : PyCharm
# @desc           : 帮助中心相关API接口

from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from apps.admin.depts import get_admin_db, get_admin_mongo_db, get_admin_redis, get_admin_paging, get_admin_id_list, Paging, IdList, SuccessResponse
from apps.admin.services.help import HelpService
from apps.admin.schemas.help import (
    IssueCategoryOut, IssueCategoryCreate, IssueCategoryUpdate,
    IssueOut, IssueCreate, IssueUpdate
)
from apps.admin.params.help import (
    IssueCategoryParams, IssueParams
)

app = APIRouter()


###########################################################
#    问题分类管理
###########################################################
@app.get("/issue/categories", summary="获取问题分类列表")
async def get_issue_categories(
    params: IssueCategoryParams = Depends(),
    paging: Paging = get_admin_paging(),
    db: AsyncSession = get_admin_db()
):
    """
    获取问题分类列表
    """
    service = HelpService(db)
    return await service.get_issue_categories(params, paging)


@app.post("/issue/categories", summary="创建问题分类")
async def create_issue_category(
    data: IssueCategoryCreate,
    db: AsyncSession = get_admin_db()
):
    """
    创建问题分类
    """
    service = HelpService(db)
    return await service.create_issue_category(data)


@app.put("/issue/categories/{data_id}", summary="更新问题分类")
async def update_issue_category(
    data_id: int,
    data: IssueCategoryUpdate,
    db: AsyncSession = get_admin_db()
):
    """
    更新问题分类
    """
    service = HelpService(db)
    return await service.update_issue_category(data_id, data)


@app.delete("/issue/categories", summary="批量删除问题分类")
async def delete_issue_categories(
    ids: IdList = get_admin_id_list(),
    db: AsyncSession = get_admin_db()
):
    """
    批量删除问题分类
    """
    service = HelpService(db)
    return await service.delete_issue_categories(ids.ids)


@app.get("/issue/categories/{data_id}", summary="获取问题分类详情")
async def get_issue_category(
    data_id: int,
    db: AsyncSession = get_admin_db()
):
    """
    获取问题分类详情
    """
    service = HelpService(db)
    return await service.get_issue_category(data_id)


###########################################################
#    问题管理
###########################################################
@app.get("/issues", summary="获取问题列表")
async def get_issues(
    params: IssueParams = Depends(),
    paging: Paging = get_admin_paging(),
    db: AsyncSession = get_admin_db()
):
    """
    获取问题列表
    """
    service = HelpService(db)
    return await service.get_issues(params, paging)


@app.post("/issues", summary="创建问题")
async def create_issue(
    data: IssueCreate,
    db: AsyncSession = get_admin_db()
):
    """
    创建问题
    """
    service = HelpService(db)
    return await service.create_issue(data)


@app.put("/issues/{data_id}", summary="更新问题")
async def update_issue(
    data_id: int,
    data: IssueUpdate,
    db: AsyncSession = get_admin_db()
):
    """
    更新问题
    """
    service = HelpService(db)
    return await service.update_issue(data_id, data)


@app.delete("/issues", summary="批量删除问题")
async def delete_issues(
    ids: IdList = get_admin_id_list(),
    db: AsyncSession = get_admin_db()
):
    """
    批量删除问题
    """
    service = HelpService(db)
    return await service.delete_issues(ids.ids)


@app.get("/issues/{data_id}", summary="获取问题详情")
async def get_issue(
    data_id: int,
    db: AsyncSession = get_admin_db()
):
    """
    获取问题详情
    """
    service = HelpService(db)
    return await service.get_issue(data_id)


@app.put("/issues/{data_id}/view", summary="增加问题查看次数")
async def increase_issue_view(
    data_id: int,
    db: AsyncSession = get_admin_db()
):
    """
    增加问题查看次数
    """
    service = HelpService(db)
    return await service.increase_issue_view(data_id)
