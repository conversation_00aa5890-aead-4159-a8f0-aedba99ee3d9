#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/20
# @File           : user.py
# @IDE            : PyCharm
# @desc           : 用户管理相关查询参数

from apps.admin.depts import QueryParams
from typing import Optional


class UserParams(QueryParams):
    """
    用户查询参数
    """
    
    def __init__(
        self,
        name: Optional[str] = None,
        telephone: Optional[str] = None,
        email: Optional[str] = None,
        is_active: Optional[bool] = None,
        is_staff: Optional[bool] = None,
        gender: Optional[str] = None,
        role_id: Optional[int] = None,
        dept_id: Optional[int] = None,
        create_datetime_start: Optional[str] = None,
        create_datetime_end: Optional[str] = None,
        params=None
    ):
        super().__init__(params)
        self.name = name
        self.telephone = telephone
        self.email = email
        self.is_active = is_active
        self.is_staff = is_staff
        self.gender = gender
        self.role_id = role_id
        self.dept_id = dept_id
        self.create_datetime_start = create_datetime_start
        self.create_datetime_end = create_datetime_end
