#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/19
# @File           : record.py
# @IDE            : PyCharm
# @desc           : 记录管理相关的Pydantic模型

from pydantic import BaseModel, ConfigDict
from apps.admin.depts import DatetimeStr
from typing import Optional, Any


# 登录记录相关模型
class LoginRecordBase(BaseModel):
    """登录记录基础模型"""
    telephone: str
    status: bool
    platform: str
    login_method: str
    ip: Optional[str] = None
    address: Optional[str] = None
    country: Optional[str] = None
    province: Optional[str] = None
    city: Optional[str] = None
    county: Optional[str] = None
    operator: Optional[str] = None
    postal_code: Optional[str] = None
    area_code: Optional[str] = None
    browser: Optional[str] = None
    system: Optional[str] = None


class LoginRecordCreate(LoginRecordBase):
    """创建登录记录模型"""
    response: Optional[str] = None
    request: Optional[str] = None


class LoginRecordOut(LoginRecordBase):
    """登录记录输出模型"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    create_datetime: DatetimeStr
    update_datetime: DatetimeStr


# 短信记录相关模型
class SMSRecordBase(BaseModel):
    """短信记录基础模型"""
    user_id: int
    status: bool
    content: str
    telephone: str
    desc: Optional[str] = None
    scene: Optional[str] = None


class SMSRecordCreate(SMSRecordBase):
    """创建短信记录模型"""
    pass


class SMSRecordOut(SMSRecordBase):
    """短信记录输出模型"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    create_datetime: DatetimeStr
    update_datetime: DatetimeStr


# 操作记录相关模型（MongoDB）
class OperationRecordBase(BaseModel):
    """操作记录基础模型"""
    user_id: int
    method: str
    path: str
    ip: Optional[str] = None
    user_agent: Optional[str] = None
    status_code: int
    request_body: Optional[dict] = None
    response_body: Optional[dict] = None
    execution_time: Optional[float] = None


class OperationRecordCreate(OperationRecordBase):
    """创建操作记录模型"""
    pass


class OperationRecordOut(OperationRecordBase):
    """操作记录输出模型"""
    model_config = ConfigDict(from_attributes=True)
    
    id: str  # MongoDB ObjectId
    create_datetime: DatetimeStr


# 统计分析相关模型
class LoginDistributeOut(BaseModel):
    """登录分布统计输出模型"""
    date: str
    success_count: int
    fail_count: int
    total_count: int


class LoginLatestOut(BaseModel):
    """最新登录记录输出模型"""
    telephone: str
    ip: Optional[str] = None
    address: Optional[str] = None
    browser: Optional[str] = None
    system: Optional[str] = None
    create_datetime: DatetimeStr


class LoginCountOut(BaseModel):
    """登录统计输出模型"""
    today_count: int
    yesterday_count: int
    week_count: int
    month_count: int
    total_count: int


class UserAnalysisOut(BaseModel):
    """用户分析统计输出模型"""
    total_users: int
    active_users: int
    new_users_today: int
    new_users_week: int
    new_users_month: int


class SystemAnalysisOut(BaseModel):
    """系统分析统计输出模型"""
    total_requests: int
    success_requests: int
    error_requests: int
    avg_response_time: float
