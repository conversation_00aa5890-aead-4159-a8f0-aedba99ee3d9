#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/19
# @File           : record.py
# @IDE            : PyCharm
# @desc           : 记录管理相关查询参数

from apps.admin.depts import QueryParams
from typing import Optional


class LoginRecordParams(QueryParams):
    """
    登录记录查询参数
    """
    
    def __init__(
        self,
        telephone: Optional[str] = None,
        status: Optional[bool] = None,
        platform: Optional[str] = None,
        login_method: Optional[str] = None,
        ip: Optional[str] = None,
        address: Optional[str] = None,
        create_datetime_start: Optional[str] = None,
        create_datetime_end: Optional[str] = None,
        params=None
    ):
        super().__init__(params)
        self.telephone = telephone
        self.status = status
        self.platform = platform
        self.login_method = login_method
        self.ip = ip
        self.address = address
        self.create_datetime_start = create_datetime_start
        self.create_datetime_end = create_datetime_end


class SMSRecordParams(QueryParams):
    """
    短信记录查询参数
    """
    
    def __init__(
        self,
        telephone: Optional[str] = None,
        status: Optional[bool] = None,
        content: Optional[str] = None,
        scene: Optional[str] = None,
        user_id: Optional[int] = None,
        create_datetime_start: Optional[str] = None,
        create_datetime_end: Optional[str] = None,
        params=None
    ):
        super().__init__(params)
        self.telephone = telephone
        self.status = status
        self.content = content
        self.scene = scene
        self.user_id = user_id
        self.create_datetime_start = create_datetime_start
        self.create_datetime_end = create_datetime_end


class OperationRecordParams(QueryParams):
    """
    操作记录查询参数
    """
    
    def __init__(
        self,
        user_id: Optional[int] = None,
        method: Optional[str] = None,
        path: Optional[str] = None,
        ip: Optional[str] = None,
        status_code: Optional[int] = None,
        create_datetime_start: Optional[str] = None,
        create_datetime_end: Optional[str] = None,
        params=None
    ):
        super().__init__(params)
        self.user_id = user_id
        self.method = method
        self.path = path
        self.ip = ip
        self.status_code = status_code
        self.create_datetime_start = create_datetime_start
        self.create_datetime_end = create_datetime_end
