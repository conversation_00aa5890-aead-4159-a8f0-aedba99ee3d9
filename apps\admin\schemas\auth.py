#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/20
# @File           : auth.py
# @IDE            : PyCharm
# @desc           : 认证相关的Pydantic模型（登录、登出、权限验证等）

from pydantic import BaseModel, ConfigDict, field_validator
from pydantic_core.core_schema import FieldValidationInfo
from apps.admin.depts import DatetimeStr, Telephone, Email
from typing import Optional, List, Dict, Any


# 登录相关模型
class LoginRequest(BaseModel):
    """登录请求模型"""
    username: str  # 可以是手机号或邮箱
    password: str
    captcha: Optional[str] = None
    captcha_key: Optional[str] = None
    remember_me: Optional[bool] = False

    @field_validator('username')
    @classmethod
    def validate_username(cls, v: str) -> str:
        if not v or len(v.strip()) == 0:
            raise ValueError('用户名不能为空')
        return v.strip()

    @field_validator('password')
    @classmethod
    def validate_password(cls, v: str) -> str:
        if not v or len(v.strip()) == 0:
            raise ValueError('密码不能为空')
        return v


class LoginResponse(BaseModel):
    """登录响应模型"""
    access_token: str
    refresh_token: str
    token_type: str = "Bearer"
    expires_in: int
    user_info: "UserProfileOut"


class RefreshTokenRequest(BaseModel):
    """刷新令牌请求模型"""
    refresh_token: str


class RefreshTokenResponse(BaseModel):
    """刷新令牌响应模型"""
    access_token: str
    token_type: str = "Bearer"
    expires_in: int


# 用户个人信息相关模型
class UserProfileOut(BaseModel):
    """用户个人信息输出模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    telephone: str
    email: Optional[str] = None
    nickname: Optional[str] = None
    avatar: Optional[str] = None
    gender: Optional[str] = None
    is_active: bool
    is_staff: bool
    last_ip: Optional[str] = None
    last_login: Optional[DatetimeStr] = None
    create_datetime: DatetimeStr

    # 权限相关
    roles: Optional[List["RoleOut"]] = []
    depts: Optional[List["DeptOut"]] = []
    permissions: Optional[List[str]] = []


class UserProfileUpdate(BaseModel):
    """用户个人信息更新模型"""
    name: Optional[str] = None
    email: Optional[Email] = None
    nickname: Optional[str] = None
    avatar: Optional[str] = None
    gender: Optional[str] = None


# 密码修改相关模型
class ChangePasswordRequest(BaseModel):
    """修改密码请求模型"""
    old_password: str
    new_password: str
    confirm_password: str

    @field_validator('new_password')
    @classmethod
    def validate_new_password(cls, v: str, info: FieldValidationInfo) -> str:
        if len(v) < 6:
            raise ValueError('新密码长度不能少于6位')
        return v

    @field_validator('confirm_password')
    @classmethod
    def validate_confirm_password(cls, v: str, info: FieldValidationInfo) -> str:
        if 'new_password' in info.data and v != info.data['new_password']:
            raise ValueError('确认密码与新密码不一致')
        return v


# 权限相关模型
class PermissionOut(BaseModel):
    """权限输出模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    permission_key: str
    menu_id: Optional[int] = None


class MenuOut(BaseModel):
    """菜单输出模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    title: str
    name: str
    path: str
    component: Optional[str] = None
    redirect: Optional[str] = None
    menu_type: str
    icon: Optional[str] = None
    order: Optional[int] = None
    parent_id: Optional[int] = None
    disabled: bool
    hidden: bool

    # 子菜单
    children: Optional[List["MenuOut"]] = []

    # 权限
    permissions: Optional[List[PermissionOut]] = []


# 角色和部门简单输出模型（用于用户信息）
class RoleOut(BaseModel):
    """角色输出模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    role_key: str
    disabled: bool
    is_admin: bool
    desc: Optional[str] = None


class DeptOut(BaseModel):
    """部门输出模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    dept_key: str
    disabled: bool
    desc: Optional[str] = None
    owner: Optional[str] = None


# 验证令牌相关模型
class TokenVerifyResponse(BaseModel):
    """令牌验证响应模型"""
    valid: bool
    user_id: Optional[int] = None
    username: Optional[str] = None
    permissions: Optional[List[str]] = []
    message: Optional[str] = None


# 验证码相关模型
class CaptchaResponse(BaseModel):
    """验证码响应模型"""
    captcha_key: str
    captcha_image: str  # base64编码的图片


# 登出相关模型
class LogoutResponse(BaseModel):
    """登出响应模型"""
    message: str = "登出成功"


# ==================== 用户管理相关模型 ====================

class UserCreate(BaseModel):
    """用户创建模型"""
    name: str
    telephone: Telephone
    email: Optional[Email] = None
    nickname: Optional[str] = None
    password: str
    gender: Optional[str] = None
    is_active: bool = True
    is_staff: bool = False
    role_ids: Optional[List[int]] = []
    dept_ids: Optional[List[int]] = []

    @field_validator('password')
    @classmethod
    def validate_password(cls, v: str) -> str:
        if len(v) < 6:
            raise ValueError('密码长度不能少于6位')
        return v


class UserUpdate(BaseModel):
    """用户更新模型"""
    name: Optional[str] = None
    telephone: Optional[Telephone] = None
    email: Optional[Email] = None
    nickname: Optional[str] = None
    gender: Optional[str] = None
    is_active: Optional[bool] = None
    is_staff: Optional[bool] = None
    role_ids: Optional[List[int]] = None
    dept_ids: Optional[List[int]] = None


class UserOut(BaseModel):
    """用户输出模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    telephone: str
    email: Optional[str] = None
    nickname: Optional[str] = None
    avatar: Optional[str] = None
    gender: Optional[str] = None
    is_active: bool
    is_staff: bool
    last_ip: Optional[str] = None
    last_login: Optional[DatetimeStr] = None
    create_datetime: DatetimeStr
    update_datetime: Optional[DatetimeStr] = None

    # 关联信息
    roles: Optional[List[RoleOut]] = []
    depts: Optional[List[DeptOut]] = []


class UserListOut(BaseModel):
    """用户列表输出模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    telephone: str
    email: Optional[str] = None
    nickname: Optional[str] = None
    is_active: bool
    is_staff: bool
    last_login: Optional[DatetimeStr] = None
    create_datetime: DatetimeStr


class ResetPasswordRequest(BaseModel):
    """重置密码请求模型"""
    new_password: str
    confirm_password: str

    @field_validator('new_password')
    @classmethod
    def validate_new_password(cls, v: str) -> str:
        if len(v) < 6:
            raise ValueError('新密码长度不能少于6位')
        return v

    @field_validator('confirm_password')
    @classmethod
    def validate_confirm_password(cls, v: str, info: FieldValidationInfo) -> str:
        if 'new_password' in info.data and v != info.data['new_password']:
            raise ValueError('确认密码与新密码不一致')
        return v


# ==================== 角色管理相关模型 ====================

class RoleCreate(BaseModel):
    """角色创建模型"""
    name: str
    role_key: str
    desc: Optional[str] = None
    disabled: bool = False
    is_admin: bool = False
    menu_ids: Optional[List[int]] = []
    dept_ids: Optional[List[int]] = []

    @field_validator('role_key')
    @classmethod
    def validate_role_key(cls, v: str) -> str:
        if not v or len(v.strip()) == 0:
            raise ValueError('角色标识不能为空')
        return v.strip()


class RoleUpdate(BaseModel):
    """角色更新模型"""
    name: Optional[str] = None
    role_key: Optional[str] = None
    desc: Optional[str] = None
    disabled: Optional[bool] = None
    is_admin: Optional[bool] = None
    menu_ids: Optional[List[int]] = None
    dept_ids: Optional[List[int]] = None


class RoleDetailOut(BaseModel):
    """角色详情输出模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    role_key: str
    disabled: bool
    is_admin: bool
    desc: Optional[str] = None
    create_datetime: DatetimeStr
    update_datetime: Optional[DatetimeStr] = None

    # 关联信息
    menus: Optional[List[MenuOut]] = []
    depts: Optional[List[DeptOut]] = []


class RoleOptionOut(BaseModel):
    """角色选项输出模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    role_key: str
    disabled: bool


# ==================== 菜单管理相关模型 ====================

class MenuCreate(BaseModel):
    """菜单创建模型"""
    title: str
    name: str
    path: str
    component: Optional[str] = None
    redirect: Optional[str] = None
    menu_type: str  # directory, menu, button
    icon: Optional[str] = None
    order: Optional[int] = 0
    parent_id: Optional[int] = None
    disabled: bool = False
    hidden: bool = False
    always_show: bool = False
    breadcrumb: bool = True
    no_cache: bool = False
    affix: bool = False

    @field_validator('name')
    @classmethod
    def validate_name(cls, v: str) -> str:
        if not v or len(v.strip()) == 0:
            raise ValueError('菜单名称不能为空')
        return v.strip()


class MenuUpdate(BaseModel):
    """菜单更新模型"""
    title: Optional[str] = None
    name: Optional[str] = None
    path: Optional[str] = None
    component: Optional[str] = None
    redirect: Optional[str] = None
    menu_type: Optional[str] = None
    icon: Optional[str] = None
    order: Optional[int] = None
    parent_id: Optional[int] = None
    disabled: Optional[bool] = None
    hidden: Optional[bool] = None
    always_show: Optional[bool] = None
    breadcrumb: Optional[bool] = None
    no_cache: Optional[bool] = None
    affix: Optional[bool] = None


class MenuDetailOut(BaseModel):
    """菜单详情输出模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    title: str
    name: str
    path: str
    component: Optional[str] = None
    redirect: Optional[str] = None
    menu_type: str
    icon: Optional[str] = None
    order: Optional[int] = None
    parent_id: Optional[int] = None
    disabled: bool
    hidden: bool
    always_show: bool
    breadcrumb: bool
    no_cache: bool
    affix: bool
    create_datetime: DatetimeStr
    update_datetime: Optional[DatetimeStr] = None

    # 子菜单
    children: Optional[List["MenuDetailOut"]] = []
    # 权限
    permissions: Optional[List[PermissionOut]] = []


class MenuTreeOut(BaseModel):
    """菜单树输出模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    title: str
    name: str
    parent_id: Optional[int] = None
    disabled: bool
    children: Optional[List["MenuTreeOut"]] = []


class MenuOptionOut(BaseModel):
    """菜单选项输出模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    title: str
    name: str
    parent_id: Optional[int] = None


# ==================== 部门管理相关模型 ====================

class DeptCreate(BaseModel):
    """部门创建模型"""
    name: str
    dept_key: str
    desc: Optional[str] = None
    disabled: bool = False
    order: Optional[int] = 0
    parent_id: Optional[int] = None
    owner: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[Email] = None

    @field_validator('dept_key')
    @classmethod
    def validate_dept_key(cls, v: str) -> str:
        if not v or len(v.strip()) == 0:
            raise ValueError('部门标识不能为空')
        return v.strip()


class DeptUpdate(BaseModel):
    """部门更新模型"""
    name: Optional[str] = None
    dept_key: Optional[str] = None
    desc: Optional[str] = None
    disabled: Optional[bool] = None
    order: Optional[int] = None
    parent_id: Optional[int] = None
    owner: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[Email] = None


class DeptDetailOut(BaseModel):
    """部门详情输出模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    dept_key: str
    disabled: bool
    desc: Optional[str] = None
    order: Optional[int] = None
    parent_id: Optional[int] = None
    owner: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    create_datetime: DatetimeStr
    update_datetime: Optional[DatetimeStr] = None

    # 子部门
    children: Optional[List["DeptDetailOut"]] = []


class DeptTreeOut(BaseModel):
    """部门树输出模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    dept_key: str
    parent_id: Optional[int] = None
    disabled: bool
    children: Optional[List["DeptTreeOut"]] = []


class DeptOptionOut(BaseModel):
    """部门选项输出模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    dept_key: str
    parent_id: Optional[int] = None
