#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/21
# @File           : dept.py
# @IDE            : PyCharm
# @desc           : 部门管理相关API接口

from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
from apps.admin.depts import get_admin_db, get_admin_paging, Paging
from apps.admin.services.dept import DeptService
from apps.admin.schemas.dept import (
    DeptOut, DeptCreate, DeptUpdate, DeptBatchRequest
)
from apps.admin.params.dept import DeptParams
from typing import List, Optional

app = APIRouter()


@app.get("/depts", summary="获取部门列表")
async def get_depts(
    params: DeptParams = Depends(),
    paging: Paging = Depends(get_admin_paging),
    db: AsyncSession = get_admin_db()
):
    """
    获取部门列表
    """
    service = DeptService(db)
    return await service.get_depts(params, paging)


@app.post("/depts", summary="创建部门")
async def create_dept(
    data: DeptCreate,
    db: AsyncSession = get_admin_db()
):
    """
    创建部门
    """
    service = DeptService(db)
    return await service.create_dept(data)


@app.get("/depts/{dept_id}", summary="获取部门详情")
async def get_dept(
    dept_id: int,
    db: AsyncSession = get_admin_db()
):
    """
    获取部门详情
    """
    service = DeptService(db)
    return await service.get_dept(dept_id)


@app.put("/depts/{dept_id}", summary="更新部门")
async def update_dept(
    dept_id: int,
    data: DeptUpdate,
    db: AsyncSession = get_admin_db()
):
    """
    更新部门
    """
    service = DeptService(db)
    return await service.update_dept(dept_id, data)


@app.delete("/depts/{dept_id}", summary="删除部门")
async def delete_dept(
    dept_id: int,
    db: AsyncSession = get_admin_db()
):
    """
    删除部门
    """
    service = DeptService(db)
    return await service.delete_depts([dept_id])


@app.post("/depts/batch-delete", summary="批量删除部门")
async def batch_delete_depts(
    data: DeptBatchRequest,
    db: AsyncSession = get_admin_db()
):
    """
    批量删除部门
    """
    service = DeptService(db)
    return await service.delete_depts(data.dept_ids)


@app.get("/depts/tree", summary="获取部门树")
async def get_dept_tree(
    include_disabled: bool = Query(False, description="是否包含禁用的部门"),
    db: AsyncSession = get_admin_db()
):
    """
    获取部门树结构
    """
    service = DeptService(db)
    return await service.get_dept_tree(include_disabled)


@app.get("/depts/options", summary="获取部门选项")
async def get_dept_options(
    exclude_id: Optional[int] = Query(None, description="排除的部门ID"),
    db: AsyncSession = get_admin_db()
):
    """
    获取部门选项（用于父部门选择）
    """
    service = DeptService(db)
    return await service.get_dept_options(exclude_id)


@app.get("/depts/user-tree", summary="获取部门用户树")
async def get_dept_user_tree(
    db: AsyncSession = get_admin_db()
):
    """
    获取部门用户树结构
    """
    service = DeptService(db)
    return await service.get_dept_user_tree()


@app.get("/depts/stats", summary="获取部门统计")
async def get_dept_stats(
    dept_id: Optional[int] = Query(None, description="部门ID，不传则获取所有部门统计"),
    db: AsyncSession = get_admin_db()
):
    """
    获取部门统计信息
    """
    service = DeptService(db)
    return await service.get_dept_stats(dept_id)


@app.post("/depts/{dept_id}/enable", summary="启用部门")
async def enable_dept(
    dept_id: int,
    db: AsyncSession = get_admin_db()
):
    """
    启用部门
    """
    service = DeptService(db)
    return await service.update_dept(dept_id, DeptUpdate(disabled=False))


@app.post("/depts/{dept_id}/disable", summary="禁用部门")
async def disable_dept(
    dept_id: int,
    db: AsyncSession = get_admin_db()
):
    """
    禁用部门
    """
    service = DeptService(db)
    return await service.update_dept(dept_id, DeptUpdate(disabled=True))
