#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/20
# @File           : user.py
# @IDE            : PyCharm
# @desc           : 用户管理相关服务

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_
from apps.admin.depts import AdminService
from models.admin.auth.user import AdminUser
from models.admin.auth.role import AdminRole
from models.admin.auth.dept import AdminDept
from apps.admin.schemas.user import (
    UserOut, UserCreate, UserUpdate, UserPasswordUpdate, 
    UserListOut, UserSimpleOut, RoleSimpleOut, DeptSimpleOut
)
from apps.admin.params.user import UserParams
from apps.admin.depts import Paging, CustomException, SuccessResponse
from passlib.context import CryptContext
from typing import Any

pwd_context = CryptContext(schemes=['bcrypt'], deprecated='auto')


class UserService(AdminService):
    """
    用户管理相关服务类
    """
    
    def __init__(self, db: AsyncSession):
        super().__init__(db)
        self.user_dal = self.get_dal(AdminUser, UserOut)
        self.role_dal = self.get_dal(AdminRole)
        self.dept_dal = self.get_dal(AdminDept)
    
    async def get_users(self, params: UserParams, paging: Paging) -> dict:
        """
        获取用户列表
        """
        # 构建查询条件
        filters = {}
        if params.name:
            filters["name"] = ("like", params.name)
        if params.telephone:
            filters["telephone"] = ("like", params.telephone)
        if params.email:
            filters["email"] = ("like", params.email)
        if params.is_active is not None:
            filters["is_active"] = params.is_active
        if params.is_staff is not None:
            filters["is_staff"] = params.is_staff
        if params.gender:
            filters["gender"] = params.gender
        
        # 时间范围过滤
        if params.create_datetime_start:
            filters["create_datetime"] = (">=", params.create_datetime_start)
        if params.create_datetime_end:
            filters["create_datetime"] = ("<=", params.create_datetime_end)
        
        # 获取数据
        datas, count = await self.user_dal.get_datas(
            page=paging.page,
            limit=paging.limit,
            v_return_count=True,
            v_order=paging.v_order,
            v_schema=UserListOut,
            **filters
        )
        
        return SuccessResponse(
            data={
                "items": datas,
                "total": count,
                "page": paging.page,
                "limit": paging.limit
            },
            msg="获取用户列表成功"
        )
    
    async def create_user(self, data: UserCreate) -> dict:
        """
        创建用户
        """
        # 检查手机号是否已存在
        existing_user = await self.user_dal.get_data(telephone=data.telephone, v_return_none=True)
        if existing_user:
            raise CustomException("手机号已存在")
        
        # 检查邮箱是否已存在（如果提供了邮箱）
        if data.email:
            existing_email = await self.user_dal.get_data(email=data.email, v_return_none=True)
            if existing_email:
                raise CustomException("邮箱已存在")
        
        # 加密密码
        hashed_password = pwd_context.hash(data.password)
        
        # 创建用户数据
        user_data = data.model_dump(exclude={"password", "role_ids", "dept_ids"})
        user_data["password"] = hashed_password
        
        # 创建用户
        user = await self.user_dal.create_data(data=user_data, v_schema=UserOut)
        
        # 分配角色
        if data.role_ids:
            await self.assign_user_roles(user.id, data.role_ids)
        
        # 分配部门
        if data.dept_ids:
            await self.assign_user_depts(user.id, data.dept_ids)
        
        return SuccessResponse(data=user, msg="创建用户成功")
    
    async def get_user(self, user_id: int) -> dict:
        """
        获取用户详情
        """
        user = await self.user_dal.get_data(user_id, v_schema=UserOut)
        if not user:
            raise CustomException("用户不存在")
        
        return SuccessResponse(data=user, msg="获取用户详情成功")
    
    async def update_user(self, user_id: int, data: UserUpdate) -> dict:
        """
        更新用户
        """
        # 检查用户是否存在
        user = await self.user_dal.get_data(user_id)
        if not user:
            raise CustomException("用户不存在")
        
        # 检查手机号是否被其他用户使用
        if data.telephone:
            existing_user = await self.user_dal.get_data(
                telephone=data.telephone, 
                v_return_none=True
            )
            if existing_user and existing_user.id != user_id:
                raise CustomException("手机号已被其他用户使用")
        
        # 检查邮箱是否被其他用户使用
        if data.email:
            existing_email = await self.user_dal.get_data(
                email=data.email, 
                v_return_none=True
            )
            if existing_email and existing_email.id != user_id:
                raise CustomException("邮箱已被其他用户使用")
        
        # 更新用户数据
        update_data = data.model_dump(exclude_unset=True, exclude={"role_ids", "dept_ids"})
        updated_user = await self.user_dal.put_data(user_id, update_data, v_schema=UserOut)
        
        # 更新角色分配
        if data.role_ids is not None:
            await self.assign_user_roles(user_id, data.role_ids)
        
        # 更新部门分配
        if data.dept_ids is not None:
            await self.assign_user_depts(user_id, data.dept_ids)
        
        return SuccessResponse(data=updated_user, msg="更新用户成功")
    
    async def update_user_password(self, user_id: int, data: UserPasswordUpdate) -> dict:
        """
        修改用户密码
        """
        # 检查用户是否存在
        user = await self.user_dal.get_data(user_id)
        if not user:
            raise CustomException("用户不存在")
        
        # 验证旧密码
        if not pwd_context.verify(data.old_password, user.password):
            raise CustomException("旧密码错误")
        
        # 加密新密码
        hashed_password = pwd_context.hash(data.new_password)
        
        # 更新密码
        await self.user_dal.put_data(user_id, {"password": hashed_password})
        
        return SuccessResponse(msg="密码修改成功")
    
    async def delete_user(self, user_id: int) -> dict:
        """
        删除用户
        """
        # 检查用户是否存在
        user = await self.user_dal.get_data(user_id)
        if not user:
            raise CustomException("用户不存在")
        
        # 软删除用户
        await self.user_dal.delete_datas([user_id], v_soft=True)
        
        return SuccessResponse(msg="删除用户成功")
    
    async def assign_user_roles(self, user_id: int, role_ids: list[int]) -> dict:
        """
        分配用户角色
        """
        # 检查用户是否存在
        user = await self.user_dal.get_data(user_id)
        if not user:
            raise CustomException("用户不存在")
        
        # 检查角色是否存在
        if role_ids:
            roles = await self.role_dal.get_datas(v_where=[AdminRole.id.in_(role_ids)])
            if len(roles) != len(role_ids):
                raise CustomException("部分角色不存在")
        
        # 清除现有角色关联
        user.roles.clear()
        
        # 添加新的角色关联
        if role_ids:
            roles = await self.role_dal.get_datas(v_where=[AdminRole.id.in_(role_ids)])
            user.roles.extend(roles)
        
        await self.db.commit()
        
        return SuccessResponse(msg="分配用户角色成功")
    
    async def assign_user_depts(self, user_id: int, dept_ids: list[int]) -> dict:
        """
        分配用户部门
        """
        # 检查用户是否存在
        user = await self.user_dal.get_data(user_id)
        if not user:
            raise CustomException("用户不存在")
        
        # 检查部门是否存在
        if dept_ids:
            depts = await self.dept_dal.get_datas(v_where=[AdminDept.id.in_(dept_ids)])
            if len(depts) != len(dept_ids):
                raise CustomException("部分部门不存在")
        
        # 清除现有部门关联
        user.depts.clear()
        
        # 添加新的部门关联
        if dept_ids:
            depts = await self.dept_dal.get_datas(v_where=[AdminDept.id.in_(dept_ids)])
            user.depts.extend(depts)
        
        await self.db.commit()
        
        return SuccessResponse(msg="分配用户部门成功")
    
    async def get_user_roles(self, user_id: int) -> dict:
        """
        获取用户角色
        """
        user = await self.user_dal.get_data(user_id)
        if not user:
            raise CustomException("用户不存在")
        
        roles = [RoleSimpleOut.model_validate(role) for role in user.roles]
        
        return SuccessResponse(data=roles, msg="获取用户角色成功")
    
    async def get_user_depts(self, user_id: int) -> dict:
        """
        获取用户部门
        """
        user = await self.user_dal.get_data(user_id)
        if not user:
            raise CustomException("用户不存在")
        
        depts = [DeptSimpleOut.model_validate(dept) for dept in user.depts]
        
        return SuccessResponse(data=depts, msg="获取用户部门成功")
    
    async def batch_delete_users(self, user_ids: list[int]) -> dict:
        """
        批量删除用户
        """
        if not user_ids:
            raise CustomException("请选择要删除的用户")
        
        # 软删除用户
        await self.user_dal.delete_datas(user_ids, v_soft=True)
        
        return SuccessResponse(msg=f"批量删除 {len(user_ids)} 个用户成功")
    
    async def enable_user(self, user_id: int) -> dict:
        """
        启用用户
        """
        await self.user_dal.put_data(user_id, {"is_active": True})
        return SuccessResponse(msg="启用用户成功")
    
    async def disable_user(self, user_id: int) -> dict:
        """
        禁用用户
        """
        await self.user_dal.put_data(user_id, {"is_active": False})
        return SuccessResponse(msg="禁用用户成功")
    
    async def reset_user_password(self, user_id: int) -> dict:
        """
        重置用户密码
        """
        # 检查用户是否存在
        user = await self.user_dal.get_data(user_id)
        if not user:
            raise CustomException("用户不存在")
        
        # 重置为默认密码
        default_password = "123456"
        hashed_password = pwd_context.hash(default_password)
        
        await self.user_dal.put_data(user_id, {
            "password": hashed_password,
            "is_reset_password": True
        })
        
        return SuccessResponse(msg="重置用户密码成功，默认密码：123456")
