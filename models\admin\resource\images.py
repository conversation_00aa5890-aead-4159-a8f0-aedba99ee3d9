#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2023/8/25 13:41
# @File           : images.py
# @IDE            : PyCharm
# @desc           : 图片素材表

from datetime import datetime
from sqlalchemy.orm import relationship, Mapped, mapped_column
from models.admin.auth.user import AdminUser
from models.base import BaseModel
from sqlalchemy import String, ForeignKey, Integer, DateTime, func, Boolean


class AdminImages(BaseModel):
    __tablename__ = "admin_resource_images"
    __table_args__ = ({'comment': '图片素材表'})

    # 基础字段
    id: Mapped[int] = mapped_column(Integer, primary_key=True, comment='主键ID')
    create_datetime: Mapped[datetime] = mapped_column(DateTime, server_default=func.now(), comment='创建时间')
    update_datetime: Mapped[datetime] = mapped_column(
        DateTime,
        server_default=func.now(),
        onupdate=func.now(),
        comment='更新时间'
    )
    delete_datetime: Mapped[datetime | None] = mapped_column(DateTime, nullable=True, comment='删除时间')
    is_delete: Mapped[bool] = mapped_column(<PERSON><PERSON>an, default=False, comment="是否软删除")

    # 业务字段
    filename: Mapped[str] = mapped_column(String(255), nullable=False, comment="原图片名称")
    image_url: Mapped[str] = mapped_column(String(500), nullable=False, comment="图片链接")

    create_user_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("admin_auth_user.id", ondelete='RESTRICT'),
        comment="创建人"
    )
    create_user: Mapped[AdminUser] = relationship(foreign_keys=create_user_id)
