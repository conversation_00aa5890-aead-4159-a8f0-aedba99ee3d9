#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/19
# @File           : system.py
# @IDE            : PyCharm
# @desc           : 系统管理相关服务

from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import UploadFile, Request
from apps.admin.depts import AdminService
from models.admin.system.dict import AdminDictType, AdminDictDetails
from models.admin.system.settings import AdminSystemSettings, AdminSystemSettingsTab
from apps.admin.schemas.system import (
    DictTypeOut, DictTypeCreate, DictTypeUpdate,
    DictDetailsOut, DictDetailsCreate, DictDetailsUpdate,
    SettingsOut, SettingsCreate, SettingsUpdate,
    SettingsTabOut
)
from apps.admin.params.system import (
    DictTypeParams, DictDetailsParams, SettingsParams
)
from apps.admin.depts import Paging, CustomException, SuccessResponse
from typing import Any


class SystemService(AdminService):
    """
    系统管理相关服务类
    """
    
    def __init__(self, db: AsyncSession):
        super().__init__(db)
        self.dict_type_dal = self.get_dal(AdminDictType, DictTypeOut)
        self.dict_details_dal = self.get_dal(AdminDictDetails, DictDetailsOut)
        self.settings_dal = self.get_dal(AdminSystemSettings, SettingsOut)
        self.settings_tab_dal = self.get_dal(AdminSystemSettingsTab, SettingsTabOut)
    
    # 字典类型管理
    async def get_dict_types(self, params: DictTypeParams, paging: Paging) -> dict:
        """
        获取字典类型列表
        """
        filters = {}
        if params.dict_name:
            filters["dict_name"] = ("like", params.dict_name)
        if params.dict_type:
            filters["dict_type"] = ("like", params.dict_type)
        if params.disabled is not None:
            filters["disabled"] = params.disabled
        
        datas, count = await self.dict_type_dal.get_datas(
            page=paging.page,
            limit=paging.limit,
            v_return_count=True,
            v_order=paging.v_order,
            v_order_field=paging.v_order_field,
            **filters
        )
        
        return SuccessResponse(data=datas, count=count)
    
    async def create_dict_type(self, data: DictTypeCreate) -> dict:
        """
        创建字典类型
        """
        # 检查字典类型是否已存在
        existing = await self.dict_type_dal.get_data(
            dict_type=data.dict_type,
            v_return_none=True
        )
        if existing:
            raise CustomException("字典类型已存在")
        
        result = await self.dict_type_dal.create_data(data.model_dump())
        return SuccessResponse(data=result, msg="创建成功")
    
    async def update_dict_type(self, data_id: int, data: DictTypeUpdate) -> dict:
        """
        更新字典类型
        """
        await self.dict_type_dal.get_data(data_id)
        result = await self.dict_type_dal.put_data(data_id, data.model_dump(exclude_unset=True))
        return SuccessResponse(data=result, msg="更新成功")
    
    async def delete_dict_types(self, ids: list[int]) -> dict:
        """
        删除字典类型
        """
        await self.dict_type_dal.delete_datas(ids, v_soft=True)
        return SuccessResponse(msg="删除成功")
    
    async def get_dict_type(self, data_id: int) -> dict:
        """
        获取字典类型详情
        """
        data = await self.dict_type_dal.get_data(data_id)
        return SuccessResponse(data=data)
    
    # 字典详情管理
    async def get_dict_details(self, params: DictDetailsParams, paging: Paging) -> dict:
        """
        获取字典详情列表
        """
        filters = {}
        if params.label:
            filters["label"] = ("like", params.label)
        if params.value:
            filters["value"] = ("like", params.value)
        if params.disabled is not None:
            filters["disabled"] = params.disabled
        if params.is_default is not None:
            filters["is_default"] = params.is_default
        if params.dict_type_id:
            filters["dict_type_id"] = params.dict_type_id
        
        datas, count = await self.dict_details_dal.get_datas(
            page=paging.page,
            limit=paging.limit,
            v_return_count=True,
            v_order=paging.v_order,
            v_order_field=paging.v_order_field,
            **filters
        )
        
        return SuccessResponse(data=datas, count=count)
    
    async def create_dict_detail(self, data: DictDetailsCreate) -> dict:
        """
        创建字典详情
        """
        result = await self.dict_details_dal.create_data(data.model_dump())
        return SuccessResponse(data=result, msg="创建成功")
    
    async def update_dict_detail(self, data_id: int, data: DictDetailsUpdate) -> dict:
        """
        更新字典详情
        """
        await self.dict_details_dal.get_data(data_id)
        result = await self.dict_details_dal.put_data(data_id, data.model_dump(exclude_unset=True))
        return SuccessResponse(data=result, msg="更新成功")
    
    async def delete_dict_details(self, ids: list[int]) -> dict:
        """
        删除字典详情
        """
        await self.dict_details_dal.delete_datas(ids, v_soft=True)
        return SuccessResponse(msg="删除成功")
    
    async def get_dict_detail(self, data_id: int) -> dict:
        """
        获取字典详情
        """
        data = await self.dict_details_dal.get_data(data_id)
        return SuccessResponse(data=data)
    
    async def get_dicts_details(self, dict_types: list[str]) -> dict:
        """
        获取多个字典类型下的字典元素列表
        """
        result = {}
        for dict_type in dict_types:
            # 先获取字典类型
            type_obj = await self.dict_type_dal.get_data(
                dict_type=dict_type,
                v_return_none=True
            )
            if type_obj:
                # 获取该类型下的所有详情
                details = await self.dict_details_dal.get_datas(
                    limit=0,
                    dict_type_id=type_obj.id,
                    disabled=False,
                    v_order_field="order"
                )
                result[dict_type] = details
            else:
                result[dict_type] = []
        
        return SuccessResponse(data=result)
    
    # 系统配置管理
    async def get_settings_tabs(self, classifys: list[str]) -> dict:
        """
        获取系统配置标签列表
        """
        datas = await self.settings_tab_dal.get_datas(
            limit=0,
            classify=("in", classifys),
            disabled=False,
            v_order_field="id"
        )
        return SuccessResponse(data=datas)
    
    async def get_settings_tabs_values(self, tab_id: int) -> dict:
        """
        获取系统配置标签下的信息
        """
        datas = await self.settings_dal.get_datas(
            limit=0,
            tab_id=tab_id,
            disabled=False
        )
        result = {}
        for data in datas:
            result[data.config_key] = data.config_value
        return SuccessResponse(data=result)
    
    async def update_settings_tabs_values(self, datas: dict, request: Request) -> dict:
        """
        更新系统配置信息
        """
        for key, value in datas.items():
            setting = await self.settings_dal.get_data(
                config_key=key,
                v_return_none=True
            )
            if setting:
                await self.settings_dal.put_data(setting.id, {"config_value": value})
        
        return SuccessResponse(msg="更新成功")
    
    async def get_base_config(self) -> dict:
        """
        获取系统基础配置
        """
        # 这里可以实现获取基础配置的逻辑
        result = {
            "web_title": "Kinit管理系统",
            "web_desc": "基于FastAPI的管理系统",
            "web_logo": "",
            "web_ico": ""
        }
        return SuccessResponse(data=result)
    
    async def get_privacy(self) -> dict:
        """
        获取隐私协议
        """
        setting = await self.settings_dal.get_data(
            config_key="privacy_agreement",
            v_return_none=True
        )
        content = setting.config_value if setting else ""
        return SuccessResponse(data={"content": content})
    
    async def get_agreement(self) -> dict:
        """
        获取用户协议
        """
        setting = await self.settings_dal.get_data(
            config_key="user_agreement",
            v_return_none=True
        )
        content = setting.config_value if setting else ""
        return SuccessResponse(data={"content": content})
    
    # 文件上传相关
    async def upload_image_to_oss(self, file: UploadFile, path: str) -> dict:
        """
        上传图片到阿里云OSS
        """
        # 这里需要实现OSS上传逻辑
        # 暂时返回模拟数据
        result = {
            "filename": file.filename,
            "url": f"/media/{path}/{file.filename}",
            "size": file.size
        }
        return SuccessResponse(data=result, msg="上传成功")
    
    async def upload_image(self, file: UploadFile, path: str) -> dict:
        """
        上传图片到本地
        """
        # 这里需要实现本地上传逻辑
        # 暂时返回模拟数据
        result = {
            "filename": file.filename,
            "url": f"/media/{path}/{file.filename}",
            "size": file.size
        }
        return SuccessResponse(data=result, msg="上传成功")
