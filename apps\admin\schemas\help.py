#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/19
# @File           : help.py
# @IDE            : PyCharm
# @desc           : 帮助中心相关的Pydantic模型

from pydantic import BaseModel, ConfigDict
from apps.admin.depts import DatetimeStr
from typing import Optional


# 问题分类相关模型
class IssueCategoryBase(BaseModel):
    """问题分类基础模型"""
    name: str
    platform: str
    is_active: Optional[bool] = True


class IssueCategoryCreate(IssueCategoryBase):
    """创建问题分类模型"""
    create_user_id: int


class IssueCategoryUpdate(BaseModel):
    """更新问题分类模型"""
    name: Optional[str] = None
    platform: Optional[str] = None
    is_active: Optional[bool] = None


class IssueCategoryOut(IssueCategoryBase):
    """问题分类输出模型"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    create_user_id: int
    create_datetime: DatetimeStr
    update_datetime: DatetimeStr


class IssueCategorySimpleOut(BaseModel):
    """问题分类简单输出模型"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    name: str
    platform: str
    is_active: bool


# 问题相关模型
class IssueBase(BaseModel):
    """问题基础模型"""
    category_id: int
    title: str
    content: str
    is_active: Optional[bool] = True


class IssueCreate(IssueBase):
    """创建问题模型"""
    create_user_id: int


class IssueUpdate(BaseModel):
    """更新问题模型"""
    category_id: Optional[int] = None
    title: Optional[str] = None
    content: Optional[str] = None
    is_active: Optional[bool] = None


class IssueOut(IssueBase):
    """问题输出模型"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    view_number: int
    create_user_id: int
    create_datetime: DatetimeStr
    update_datetime: DatetimeStr


class IssueDetailOut(IssueOut):
    """问题详情输出模型"""
    category: Optional[IssueCategorySimpleOut] = None


class IssueSimpleOut(BaseModel):
    """问题简单输出模型"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    title: str
    view_number: int
    is_active: bool
    create_datetime: DatetimeStr


# 问题统计相关模型
class IssueStatsOut(BaseModel):
    """问题统计输出模型"""
    total_issues: int
    active_issues: int
    total_categories: int
    active_categories: int
    total_views: int


class CategoryStatsOut(BaseModel):
    """分类统计输出模型"""
    category_id: int
    category_name: str
    issue_count: int
    total_views: int
