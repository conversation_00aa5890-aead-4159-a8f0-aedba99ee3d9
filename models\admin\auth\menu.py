#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2022/7/7 13:41
# @File           : menu.py
# @IDE            : PyCharm
# @desc           : 菜单模型

from sqlalchemy.orm import Mapped, mapped_column
from datetime import datetime
from sqlalchemy import DateTime, Integer, func, Boolean
from models.base import BaseModel
from sqlalchemy import String, Boolean, Integer


class AdminMenu(BaseModel):
    __tablename__ = "admin_auth_menu"
    # 基础字段
    id: Mapped[int] = mapped_column(Integer, primary_key=True, comment='主键ID')
    create_datetime: Mapped[datetime] = mapped_column(DateTime, server_default=func.now(), comment='创建时间')
    update_datetime: Mapped[datetime] = mapped_column(
        DateTime,
        server_default=func.now(),
        onupdate=func.now(),
        comment='更新时间'
    )
    delete_datetime: Mapped[datetime | None] = mapped_column(DateTime, nullable=True, comment='删除时间')
    is_delete: Mapped[bool] = mapped_column(<PERSON><PERSON><PERSON>, default=False, comment="是否软删除")

    __table_args__ = ({'comment': '菜单表'})

    title: Mapped[str] = mapped_column(String(50), comment="标题")
    icon: Mapped[str | None] = mapped_column(String(100), comment="图标")
    path: Mapped[str | None] = mapped_column(String(128), comment="路由地址")
    name: Mapped[str | None] = mapped_column(String(50), comment="路由名称")
    component: Mapped[str | None] = mapped_column(String(255), comment="组件路径")
    redirect: Mapped[str | None] = mapped_column(String(255), comment="重定向地址")
    disabled: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否禁用")
    hidden: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否隐藏")
    order: Mapped[int | None] = mapped_column(Integer, comment="排序")
    menu_type: Mapped[str] = mapped_column(String(8), comment="菜单类型")
    parent_id: Mapped[int | None] = mapped_column(Integer, comment="父菜单ID")
