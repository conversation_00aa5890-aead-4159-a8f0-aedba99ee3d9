#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/19
# @File           : system.py
# @IDE            : PyCharm
# @desc           : 系统管理相关API接口

from fastapi import APIRouter, Depends, Body, UploadFile, Form, Request
from sqlalchemy.ext.asyncio import AsyncSession

try:
    from motor.motor_asyncio import AsyncIOMotorDatabase
except ImportError:
    AsyncIOMotorDatabase = None

try:
    from redis.asyncio import Redis
except ImportError:
    Redis = None
from apps.admin.depts import (
    get_admin_db, get_admin_mongo_db, get_admin_redis,
    get_admin_paging, get_admin_id_list, Paging, IdList
)
from apps.admin.services.system import SystemService
from apps.admin.schemas.system import (
    DictTypeOut, DictTypeCreate, DictTypeUpdate,
    DictDetailsOut, DictDetailsCreate, DictDetailsUpdate,
    SettingsOut, SettingsCreate, SettingsUpdate
)
from apps.admin.params.system import (
    DictTypeParams, DictDetailsParams, SettingsParams
)

app = APIRouter()


###########################################################
#    字典类型管理
###########################################################
@app.get("/dict/types", summary="获取字典类型列表")
async def get_dict_types(
    params: DictTypeParams = Depends(),
    paging: Paging = get_admin_paging(),
    db: AsyncSession = get_admin_db()
):
    """
    获取字典类型列表
    """
    service = SystemService(db)
    return await service.get_dict_types(params, paging)


@app.post("/dict/types", summary="创建字典类型")
async def create_dict_type(
    data: DictTypeCreate,
    db: AsyncSession = get_admin_db()
):
    """
    创建字典类型
    """
    service = SystemService(db)
    return await service.create_dict_type(data)


@app.delete("/dict/types", summary="批量删除字典类型")
async def delete_dict_types(
    ids: IdList = get_admin_id_list(),
    db: AsyncSession = get_admin_db()
):
    """
    批量删除字典类型
    """
    service = SystemService(db)
    return await service.delete_dict_types(ids.ids)


@app.put("/dict/types/{data_id}", summary="更新字典类型")
async def update_dict_type(
    data_id: int,
    data: DictTypeUpdate,
    db: AsyncSession = get_admin_db()
):
    """
    更新字典类型
    """
    service = SystemService(db)
    return await service.update_dict_type(data_id, data)


@app.get("/dict/types/{data_id}", summary="获取字典类型详情")
async def get_dict_type(
    data_id: int,
    db: AsyncSession = get_admin_db()
):
    """
    获取字典类型详情
    """
    service = SystemService(db)
    return await service.get_dict_type(data_id)


@app.post("/dict/types/details", summary="获取多个字典类型下的字典元素列表")
async def get_dicts_details(
    dict_types: list[str] = Body(..., title="字典类型列表"),
    db: AsyncSession = get_admin_db()
):
    """
    获取多个字典类型下的字典元素列表
    """
    service = SystemService(db)
    return await service.get_dicts_details(dict_types)


###########################################################
#    字典元素管理
###########################################################
@app.get("/dict/details", summary="获取字典元素列表")
async def get_dict_details(
    params: DictDetailsParams = Depends(),
    paging: Paging = get_admin_paging(),
    db: AsyncSession = get_admin_db()
):
    """
    获取字典元素列表
    """
    service = SystemService(db)
    return await service.get_dict_details(params, paging)


@app.post("/dict/details", summary="创建字典元素")
async def create_dict_detail(
    data: DictDetailsCreate,
    db: AsyncSession = get_admin_db()
):
    """
    创建字典元素
    """
    service = SystemService(db)
    return await service.create_dict_detail(data)


@app.put("/dict/details/{data_id}", summary="更新字典元素")
async def update_dict_detail(
    data_id: int,
    data: DictDetailsUpdate,
    db: AsyncSession = get_admin_db()
):
    """
    更新字典元素
    """
    service = SystemService(db)
    return await service.update_dict_detail(data_id, data)


@app.delete("/dict/details", summary="批量删除字典元素")
async def delete_dict_details(
    ids: IdList = get_admin_id_list(),
    db: AsyncSession = get_admin_db()
):
    """
    批量删除字典元素
    """
    service = SystemService(db)
    return await service.delete_dict_details(ids.ids)


@app.get("/dict/details/{data_id}", summary="获取字典元素详情")
async def get_dict_detail(
    data_id: int,
    db: AsyncSession = get_admin_db()
):
    """
    获取字典元素详情
    """
    service = SystemService(db)
    return await service.get_dict_detail(data_id)


###########################################################
#    系统配置管理
###########################################################
@app.post("/settings/tabs", summary="获取系统配置标签列表")
async def get_settings_tabs(
    classifys: list[str] = Body(...),
    db: AsyncSession = get_admin_db()
):
    """
    获取系统配置标签列表
    """
    service = SystemService(db)
    return await service.get_settings_tabs(classifys)


@app.get("/settings/tabs/values", summary="获取系统配置标签下的信息")
async def get_settings_tabs_values(
    tab_id: int,
    db: AsyncSession = get_admin_db()
):
    """
    获取系统配置标签下的信息
    """
    service = SystemService(db)
    return await service.get_settings_tabs_values(tab_id)


@app.put("/settings/tabs/values", summary="更新系统配置信息")
async def update_settings_tabs_values(
    request: Request,
    datas: dict = Body(...),
    db: AsyncSession = get_admin_db()
):
    """
    更新系统配置信息
    """
    service = SystemService(db)
    return await service.update_settings_tabs_values(datas, request)


@app.get("/settings/base/config", summary="获取系统基础配置")
async def get_setting_base_config(
    db: AsyncSession = get_admin_db()
):
    """
    获取系统基础配置
    """
    service = SystemService(db)
    return await service.get_base_config()


@app.get("/settings/privacy", summary="获取隐私协议")
async def get_settings_privacy(
    db: AsyncSession = get_admin_db()
):
    """
    获取隐私协议
    """
    service = SystemService(db)
    return await service.get_privacy()


@app.get("/settings/agreement", summary="获取用户协议")
async def get_settings_agreement(
    db: AsyncSession = get_admin_db()
):
    """
    获取用户协议
    """
    service = SystemService(db)
    return await service.get_agreement()


###########################################################
#    文件上传管理
###########################################################
@app.post("/upload/image/to/oss", summary="上传图片到阿里云OSS")
async def upload_image_to_oss(
    file: UploadFile,
    path: str = Form(...),
    db: AsyncSession = get_admin_db()
):
    """
    上传图片到阿里云OSS
    """
    service = SystemService(db)
    return await service.upload_image_to_oss(file, path)


@app.post("/upload/image", summary="上传图片到本地")
async def upload_image(
    file: UploadFile,
    path: str = Form(...),
    db: AsyncSession = get_admin_db()
):
    """
    上传图片到本地
    """
    service = SystemService(db)
    return await service.upload_image(file, path)



