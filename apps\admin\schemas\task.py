# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/20
# @File           : task.py
# @IDE            : PyCharm
# @desc           : 任务数据模型

from typing import Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator


class TaskBase(BaseModel):
    """
    任务基础模型
    """
    name: str = Field(..., description="任务名称")
    job_class: str = Field(..., description="任务类路径")
    exec_strategy: str = Field(..., description="执行策略: date/cron/interval/once")
    expression: str = Field(..., description="执行表达式")
    start_date: Optional[datetime] = Field(None, description="开始时间")
    end_date: Optional[datetime] = Field(None, description="结束时间")
    timezone: str = Field("Asia/Shanghai", description="时区")
    jitter: Optional[int] = Field(None, description="时间抖动(秒)")
    max_instances: int = Field(1, description="最大并发实例数")
    args: Optional[str] = Field(None, description="任务参数(JSON格式)")
    kwargs: Optional[str] = Field(None, description="任务关键字参数(JSON格式)")
    group: Optional[str] = Field(None, description="任务分组")
    description: Optional[str] = Field(None, description="任务描述")
    is_active: bool = Field(True, description="是否启用")

    @validator('exec_strategy')
    def validate_exec_strategy(cls, v):
        allowed_strategies = ['date', 'cron', 'interval', 'once']
        if v not in allowed_strategies:
            raise ValueError(f'执行策略必须是以下之一: {allowed_strategies}')
        return v


class TaskCreate(TaskBase):
    """
    创建任务模型
    """
    pass


class TaskUpdate(BaseModel):
    """
    更新任务模型
    """
    name: Optional[str] = Field(None, description="任务名称")
    job_class: Optional[str] = Field(None, description="任务类路径")
    exec_strategy: Optional[str] = Field(None, description="执行策略")
    expression: Optional[str] = Field(None, description="执行表达式")
    start_date: Optional[datetime] = Field(None, description="开始时间")
    end_date: Optional[datetime] = Field(None, description="结束时间")
    timezone: Optional[str] = Field(None, description="时区")
    jitter: Optional[int] = Field(None, description="时间抖动(秒)")
    max_instances: Optional[int] = Field(None, description="最大并发实例数")
    args: Optional[str] = Field(None, description="任务参数(JSON格式)")
    kwargs: Optional[str] = Field(None, description="任务关键字参数(JSON格式)")
    group: Optional[str] = Field(None, description="任务分组")
    description: Optional[str] = Field(None, description="任务描述")
    is_active: Optional[bool] = Field(None, description="是否启用")


class TaskOut(TaskBase):
    """
    任务输出模型
    """
    id: int = Field(..., description="任务ID")
    create_datetime: datetime = Field(..., description="创建时间")
    update_datetime: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class TaskListOut(BaseModel):
    """
    任务列表输出模型
    """
    id: int = Field(..., description="任务ID")
    name: str = Field(..., description="任务名称")
    job_class: str = Field(..., description="任务类路径")
    exec_strategy: str = Field(..., description="执行策略")
    expression: str = Field(..., description="执行表达式")
    group: Optional[str] = Field(None, description="任务分组")
    is_active: bool = Field(..., description="是否启用")
    create_datetime: datetime = Field(..., description="创建时间")

    class Config:
        from_attributes = True


class TaskRecordOut(BaseModel):
    """
    任务执行记录输出模型
    """
    id: int = Field(..., description="记录ID")
    job_id: str = Field(..., description="任务ID")
    job_class: Optional[str] = Field(None, description="任务类路径")
    name: Optional[str] = Field(None, description="任务名称")
    group: Optional[str] = Field(None, description="任务分组")
    exec_strategy: Optional[str] = Field(None, description="执行策略")
    expression: Optional[str] = Field(None, description="执行表达式")
    start_time: datetime = Field(..., description="开始执行时间")
    end_time: datetime = Field(..., description="结束执行时间")
    process_time: float = Field(..., description="执行耗时(秒)")
    retval: Optional[str] = Field(None, description="返回值")
    exception: Optional[str] = Field(None, description="异常信息")
    traceback: Optional[str] = Field(None, description="堆栈跟踪")
    create_datetime: datetime = Field(..., description="创建时间")

    class Config:
        from_attributes = True


class TaskExecuteRequest(BaseModel):
    """
    立即执行任务请求模型
    """
    args: Optional[str] = Field(None, description="任务参数(JSON格式)")
    kwargs: Optional[str] = Field(None, description="任务关键字参数(JSON格式)")


class TaskStatusResponse(BaseModel):
    """
    任务状态响应模型
    """
    job_id: str = Field(..., description="任务ID")
    is_running: bool = Field(..., description="是否正在运行")
    next_run_time: Optional[datetime] = Field(None, description="下次运行时间")
    last_run_time: Optional[datetime] = Field(None, description="上次运行时间")
