#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/21
# @File           : role.py
# @IDE            : PyCharm
# @desc           : 角色管理相关数据模型

from pydantic import BaseModel, ConfigDict, field_validator
from typing import Optional, List
from core.data_types import DatetimeStr


class RoleCreate(BaseModel):
    """角色创建模型"""
    name: str
    role_key: str
    desc: Optional[str] = None
    disabled: bool = False
    is_admin: bool = False
    menu_ids: Optional[List[int]] = []
    dept_ids: Optional[List[int]] = []

    @field_validator('name')
    @classmethod
    def validate_name(cls, v: str) -> str:
        if not v or len(v.strip()) == 0:
            raise ValueError('角色名称不能为空')
        return v.strip()

    @field_validator('role_key')
    @classmethod
    def validate_role_key(cls, v: str) -> str:
        if not v or len(v.strip()) == 0:
            raise ValueError('角色标识不能为空')
        return v.strip()


class RoleUpdate(BaseModel):
    """角色更新模型"""
    name: Optional[str] = None
    role_key: Optional[str] = None
    desc: Optional[str] = None
    disabled: Optional[bool] = None
    is_admin: Optional[bool] = None
    menu_ids: Optional[List[int]] = None
    dept_ids: Optional[List[int]] = None


class RoleOut(BaseModel):
    """角色输出模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    role_key: str
    disabled: bool
    is_admin: bool
    desc: Optional[str] = None
    create_datetime: DatetimeStr
    update_datetime: Optional[DatetimeStr] = None

    # 关联信息
    menus: Optional[List] = []
    depts: Optional[List] = []


class RoleListOut(BaseModel):
    """角色列表输出模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    role_key: str
    disabled: bool
    is_admin: bool
    desc: Optional[str] = None
    create_datetime: DatetimeStr


class RoleOptionOut(BaseModel):
    """角色选项输出模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    role_key: str
    disabled: bool


class RoleBatchRequest(BaseModel):
    """角色批量操作请求模型"""
    role_ids: List[int]

    @field_validator('role_ids')
    @classmethod
    def validate_role_ids(cls, v: List[int]) -> List[int]:
        if not v or len(v) == 0:
            raise ValueError('角色ID列表不能为空')
        return v


class RoleMenuRequest(BaseModel):
    """角色菜单分配请求模型"""
    menu_ids: List[int]


class RoleDeptRequest(BaseModel):
    """角色部门分配请求模型"""
    dept_ids: List[int]
