#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2022/7/7 13:41
# @File           : settings.py
# @IDE            : PyCharm
# @desc           : 系统配置模型

from datetime import datetime
from sqlalchemy.orm import relationship, Mapped, mapped_column
from models.base import BaseModel
from sqlalchemy import String, Integer, ForeignKey, Boolean, Text, DateTime, func


class AdminSystemSettingsTab(BaseModel):
    __tablename__ = "admin_system_settings_tab"
    __table_args__ = ({'comment': '系统配置分类表'})

    # 基础字段
    id: Mapped[int] = mapped_column(Integer, primary_key=True, comment='主键ID')
    create_datetime: Mapped[datetime] = mapped_column(DateTime, server_default=func.now(), comment='创建时间')
    update_datetime: Mapped[datetime] = mapped_column(
        DateTime,
        server_default=func.now(),
        onupdate=func.now(),
        comment='更新时间'
    )
    delete_datetime: Mapped[datetime | None] = mapped_column(DateTime, nullable=True, comment='删除时间')
    is_delete: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否软删除")

    # 业务字段
    title: Mapped[str] = mapped_column(String(255), comment="标题")
    classify: Mapped[str] = mapped_column(String(255), index=True, nullable=False, comment="分类键")
    tab_label: Mapped[str] = mapped_column(String(255), comment="tab标题")
    tab_name: Mapped[str] = mapped_column(String(255), index=True, nullable=False, unique=True, comment="tab标识符")
    hidden: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否隐藏")
    disabled: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否禁用")

    settings: Mapped[list["AdminSystemSettings"]] = relationship(back_populates="tab")


class AdminSystemSettings(BaseModel):
    __tablename__ = "admin_system_settings"
    __table_args__ = ({'comment': '系统配置表'})

    # 基础字段
    id: Mapped[int] = mapped_column(Integer, primary_key=True, comment='主键ID')
    create_datetime: Mapped[datetime] = mapped_column(DateTime, server_default=func.now(), comment='创建时间')
    update_datetime: Mapped[datetime] = mapped_column(
        DateTime,
        server_default=func.now(),
        onupdate=func.now(),
        comment='更新时间'
    )
    delete_datetime: Mapped[datetime | None] = mapped_column(DateTime, nullable=True, comment='删除时间')
    is_delete: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否软删除")

    # 业务字段
    config_label: Mapped[str] = mapped_column(String(255), comment="配置表标签")
    config_key: Mapped[str] = mapped_column(String(255), index=True, nullable=False, unique=True, comment="配置表键")
    config_value: Mapped[str | None] = mapped_column(Text, comment="配置表内容")
    remark: Mapped[str | None] = mapped_column(String(255), comment="备注信息")
    disabled: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否禁用")

    tab_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("admin_system_settings_tab.id", ondelete='CASCADE'),
        comment="关联tab标签"
    )
    tab: Mapped[AdminSystemSettingsTab] = relationship(foreign_keys=tab_id, back_populates="settings")
