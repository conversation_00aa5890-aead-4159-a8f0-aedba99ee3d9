#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/21
# @File           : dept.py
# @IDE            : PyCharm
# @desc           : 部门管理查询参数

from apps.admin.depts import QueryParams
from typing import Optional


class DeptParams(QueryParams):
    """
    部门查询参数
    """

    def __init__(
        self,
        name: Optional[str] = None,
        dept_key: Optional[str] = None,
        parent_id: Optional[int] = None,
        disabled: Optional[bool] = None,
        owner: Optional[str] = None,
        create_datetime_start: Optional[str] = None,
        create_datetime_end: Optional[str] = None,
        params=None
    ):
        super().__init__(params)
        self.name = name
        self.dept_key = dept_key
        self.parent_id = parent_id
        self.disabled = disabled
        self.owner = owner
        self.create_datetime_start = create_datetime_start
        self.create_datetime_end = create_datetime_end
