#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/19
# @File           : depts.py
# @IDE            : PyCharm
# @desc           : Admin应用与Core模块对接的解耦层

"""
这个文件作为apps/admin与core模块对接的解耦层，实现以下功能：
1. 统一管理admin应用的依赖注入
2. 提供admin应用专用的核心功能封装
3. 实现admin应用的初始化和配置
4. 作为admin应用与core模块之间的适配器
"""

from typing import Type, Any
from sqlalchemy.ext.asyncio import AsyncSession

try:
    from motor.motor_asyncio import AsyncIOMotorDatabase
except ImportError:
    AsyncIOMotorDatabase = None

try:
    from redis.asyncio import Redis
except ImportError:
    Redis = None
from core.crud import DalBase
from core.database import db_getter, mongo_getter, redis_getter
from core.dependencies import Paging, QueryParams, IdList
from core.exception import CustomException
from core.data_types import DatetimeStr, Telephone, Email
from core.mongo_manage import MongoManage
from utils.response import SuccessResponse, ErrorResponse
from fastapi import Depends


class AdminDalBase(DalBase):
    """
    Admin应用专用的数据访问层基类
    继承自core.crud.DalBase，添加admin特有的功能
    """

    def __init__(self, db: AsyncSession = None, model: Any = None, schema: Any = None):
        super().__init__(db, model, schema)
        # 可以在这里添加admin特有的初始化逻辑

    async def get_admin_data(self, **kwargs):
        """
        Admin应用专用的数据获取方法
        可以在这里添加admin特有的业务逻辑
        """
        return await self.get_data(**kwargs)

    async def create_admin_data(self, data, **kwargs):
        """
        Admin应用专用的数据创建方法
        可以在这里添加admin特有的业务逻辑
        """
        return await self.create_data(data, **kwargs)


class AdminService:
    """
    Admin应用服务基类
    提供admin应用通用的服务功能
    """

    def __init__(self, db: AsyncSession):
        self.db = db

    def get_dal(self, model: Type, schema: Type = None) -> AdminDalBase:
        """
        获取数据访问层实例
        """
        return AdminDalBase(self.db, model, schema)

    async def validate_permissions(self, user_id: int, action: str) -> bool:
        """
        验证用户权限
        """
        # 这里可以实现admin特有的权限验证逻辑
        return True

    async def log_operation(self, user_id: int, action: str, details: dict = None):
        """
        记录操作日志
        """
        # 这里可以实现admin特有的操作日志记录逻辑
        pass


def get_admin_db() -> AsyncSession:
    """
    获取Admin应用专用的数据库会话
    这里可以添加admin特有的数据库配置或中间件
    """
    return Depends(db_getter)


def get_admin_mongo_db() -> AsyncIOMotorDatabase:
    """
    获取Admin应用专用的MongoDB数据库会话
    """
    return Depends(mongo_getter)


def get_admin_redis() -> Redis:
    """
    获取Admin应用专用的Redis会话
    """
    return Depends(redis_getter)


def get_admin_paging() -> Paging:
    """
    获取Admin应用专用的分页参数
    """
    return Depends(Paging)


def get_admin_id_list() -> IdList:
    """
    获取Admin应用专用的ID列表参数
    """
    return Depends(IdList)


def get_admin_service(service_class: Type[AdminService]):
    """
    Admin服务依赖注入工厂
    """
    def _get_service(db: AsyncSession = Depends(db_getter)):
        return service_class(db)
    return _get_service


# Admin应用配置
ADMIN_CONFIG = {
    "app_name": "admin",
    "version": "1.0.0",
    "description": "Admin管理应用",
    "default_page_size": 10,
    "max_page_size": 100,
}


def init_admin_app():
    """
    初始化Admin应用
    """
    # 这里可以添加admin应用的初始化逻辑
    # 比如注册中间件、配置路由等
    pass


def get_admin_config(key: str = None):
    """
    获取Admin应用配置
    """
    if key:
        return ADMIN_CONFIG.get(key)
    return ADMIN_CONFIG
