#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2022/3/21 11:03
# @File           : cache.py
# @IDE            : PyCharm
# @desc           : 缓存

from typing import List
from sqlalchemy import false
from sqlalchemy.future import select
from sqlalchemy.orm import joinedload
from core.logger import logger
from core.database import db_getter
from models.admin.system.settings import AdminSystemSettingsTab
import json
from redis.asyncio.client import Redis
from core.exception import CustomException
from utils import status


class Cache:

    DEFAULT_TAB_NAMES = ["wx_server", "aliyun_sms", "aliyun_oss", "web_email"]

    def __init__(self, rd: Redis):
        self.rd = rd

    async def __get_tab_name_values(self, tab_names: List[str]):
        """
        获取系统配置标签下的标签信息
        """
        async_session = db_getter()
        session = await async_session.__anext__()
        model = AdminSystemSettingsTab
        v_options = [joinedload(model.settings)]
        sql = select(model).where(
            model.is_delete == false(),
            model.tab_name.in_(tab_names),
            model.disabled == false()
            ).options(*[load for load in v_options])
        queryset = await session.execute(sql)
        datas = queryset.scalars().unique().all()
        return self.__generate_values(datas)

    @classmethod
    def __generate_values(cls, datas: List[AdminSystemSettingsTab]):
        """
        生成字典值
        """
        result = {}
        for data in datas:
            result[data.tab_name] = {}
            for setting in data.settings:
                if not setting.is_delete and not setting.disabled:
                    result[data.tab_name][setting.config_key] = setting.config_value
        return result

    async def cache_tab_names(self, tab_names: List[str] = None):
        """
        缓存系统配置标签
        """
        if not tab_names:
            tab_names = self.DEFAULT_TAB_NAMES
        try:
            values = await self.__get_tab_name_values(tab_names)
            for tab_name, value in values.items():
                await self.rd.set(f"system_settings_tab:{tab_name}", json.dumps(value))
        except Exception as e:
            logger.error(f"缓存系统配置标签失败：{e}")

    async def get_tab_name(self, tab_name: str):
        """
        获取系统配置标签
        """
        try:
            value = await self.rd.get(f"system_settings_tab:{tab_name}")
            if value:
                return json.loads(value)
            return {}
        except Exception as e:
            logger.error(f"获取系统配置标签失败：{e}")
            return {}

    async def delete_tab_name(self, tab_name: str):
        """
        删除系统配置标签缓存
        """
        try:
            await self.rd.delete(f"system_settings_tab:{tab_name}")
        except Exception as e:
            logger.error(f"删除系统配置标签缓存失败：{e}")
