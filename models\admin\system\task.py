# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/20
# @File           : task.py
# @IDE            : PyCharm
# @desc           : 定时任务模型

from sqlalchemy import Column, String, Text, Boolean, Integer, DateTime, Float
from datetime import datetime
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy import DateTime, Integer, func, Boolean
from sqlalchemy.orm import relationship
from models.base import BaseModel


class AdminTask(BaseModel):
    """
    定时任务模型
    """
    __tablename__ = "admin_system_task"
    # 基础字段
    id: Mapped[int] = mapped_column(Integer, primary_key=True, comment='主键ID')
    create_datetime: Mapped[datetime] = mapped_column(DateTime, server_default=func.now(), comment='创建时间')
    update_datetime: Mapped[datetime] = mapped_column(
        DateTime,
        server_default=func.now(),
        onupdate=func.now(),
        comment='更新时间'
    )
    delete_datetime: Mapped[datetime | None] = mapped_column(DateTime, nullable=True, comment='删除时间')
    is_delete: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否软删除")

    __table_args__ = ({'comment': '定时任务表'})

    name = Column(String(50), nullable=False, comment="任务名称")
    job_class = Column(String(255), nullable=False, comment="任务类路径")
    exec_strategy = Column(String(20), nullable=False, comment="执行策略: date/cron/interval/once")
    expression = Column(String(255), nullable=False, comment="执行表达式")
    start_date = Column(DateTime, nullable=True, comment="开始时间")
    end_date = Column(DateTime, nullable=True, comment="结束时间")
    timezone = Column(String(50), default="Asia/Shanghai", comment="时区")
    jitter = Column(Integer, nullable=True, comment="时间抖动(秒)")
    max_instances = Column(Integer, default=1, comment="最大并发实例数")
    args = Column(Text, nullable=True, comment="任务参数(JSON格式)")
    kwargs = Column(Text, nullable=True, comment="任务关键字参数(JSON格式)")
    group = Column(String(50), nullable=True, comment="任务分组")
    description = Column(Text, nullable=True, comment="任务描述")
    is_active = Column(Boolean, default=True, comment="是否启用")
    
    def __repr__(self):
        return f"<AdminTask(name='{self.name}', exec_strategy='{self.exec_strategy}')>"


class AdminTaskRecord(BaseModel):
    """
    任务执行记录模型
    """
    __tablename__ = "admin_system_task_record"
    # 基础字段
    id: Mapped[int] = mapped_column(Integer, primary_key=True, comment='主键ID')
    create_datetime: Mapped[datetime] = mapped_column(DateTime, server_default=func.now(), comment='创建时间')
    update_datetime: Mapped[datetime] = mapped_column(
        DateTime,
        server_default=func.now(),
        onupdate=func.now(),
        comment='更新时间'
    )
    delete_datetime: Mapped[datetime | None] = mapped_column(DateTime, nullable=True, comment='删除时间')
    is_delete: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否软删除")

    __table_args__ = ({'comment': '任务执行记录表'})

    job_id = Column(String(50), nullable=False, comment="任务ID")
    job_class = Column(String(255), nullable=True, comment="任务类路径")
    name = Column(String(50), nullable=True, comment="任务名称")
    group = Column(String(50), nullable=True, comment="任务分组")
    exec_strategy = Column(String(20), nullable=True, comment="执行策略")
    expression = Column(String(255), nullable=True, comment="执行表达式")
    start_time = Column(DateTime, nullable=False, comment="开始执行时间")
    end_time = Column(DateTime, nullable=False, comment="结束执行时间")
    process_time = Column(Float, default=0, comment="执行耗时(秒)")
    retval = Column(Text, nullable=True, comment="返回值")
    exception = Column(Text, nullable=True, comment="异常信息")
    traceback = Column(Text, nullable=True, comment="堆栈跟踪")
    
    def __repr__(self):
        return f"<AdminTaskRecord(job_id='{self.job_id}', start_time='{self.start_time}')>"
