#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/19
# @File           : resource.py
# @IDE            : PyCharm
# @desc           : 资源管理相关API接口

from fastapi import APIRouter, Depends, UploadFile, Form
from sqlalchemy.ext.asyncio import AsyncSession
from apps.admin.depts import get_admin_db, get_admin_paging, get_admin_id_list, Paging, IdList
from apps.admin.services.resource import ResourceService
from apps.admin.schemas.resource import (
    ImageOut, ImageCreate, ImageUpdate
)
from apps.admin.params.resource import (
    ImageParams
)

app = APIRouter()


###########################################################
#    图片资源管理
###########################################################
@app.get("/images", summary="获取图片资源列表")
async def get_images(
    params: ImageParams = Depends(),
    paging: Paging = get_admin_paging(),
    db: AsyncSession = get_admin_db()
):
    """
    获取图片资源列表
    """
    service = ResourceService(db)
    return await service.get_images(params, paging)


@app.post("/images", summary="上传图片资源")
async def upload_image(
    file: UploadFile,
    filename: str = Form(None),
    db: AsyncSession = get_admin_db()
):
    """
    上传图片资源
    """
    service = ResourceService(db)
    return await service.upload_image(file, filename)


@app.delete("/images", summary="批量删除图片资源")
async def delete_images(
    ids: IdList = get_admin_id_list(),
    db: AsyncSession = get_admin_db()
):
    """
    批量删除图片资源
    """
    service = ResourceService(db)
    return await service.delete_images(ids.ids)


@app.get("/images/{data_id}", summary="获取图片资源详情")
async def get_image(
    data_id: int,
    db: AsyncSession = get_admin_db()
):
    """
    获取图片资源详情
    """
    service = ResourceService(db)
    return await service.get_image(data_id)


@app.put("/images/{data_id}", summary="更新图片资源信息")
async def update_image(
    data_id: int,
    data: ImageUpdate,
    db: AsyncSession = get_admin_db()
):
    """
    更新图片资源信息
    """
    service = ResourceService(db)
    return await service.update_image(data_id, data)
