#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/19
# @File           : resource.py
# @IDE            : PyCharm
# @desc           : 资源管理相关服务

import os
import uuid
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import UploadFile
from apps.admin.depts import AdminService
from models.admin.resource.images import AdminImages
from apps.admin.schemas.resource import (
    ImageOut, ImageCreate, ImageUpdate
)
from apps.admin.params.resource import (
    ImageParams
)
from apps.admin.depts import Paging, CustomException, SuccessResponse
from config.setting import STATIC_ROOT, STATIC_URL
from typing import Any


class ResourceService(AdminService):
    """
    资源管理相关服务类
    """
    
    def __init__(self, db: AsyncSession):
        super().__init__(db)
        self.image_dal = self.get_dal(AdminImages, ImageOut)
    
    # 图片资源管理
    async def get_images(self, params: ImageParams, paging: Paging) -> dict:
        """
        获取图片资源列表
        """
        filters = {}
        if params.filename:
            filters["filename"] = ("like", params.filename)
        if params.image_url:
            filters["image_url"] = ("like", params.image_url)
        if params.create_user_id:
            filters["create_user_id"] = params.create_user_id
        if params.create_datetime_start:
            filters["create_datetime"] = (">=", params.create_datetime_start)
        if params.create_datetime_end:
            filters["create_datetime"] = ("<=", params.create_datetime_end)
        
        datas, count = await self.image_dal.get_datas(
            page=paging.page,
            limit=paging.limit,
            v_return_count=True,
            v_order=paging.v_order,
            v_order_field=paging.v_order_field,
            **filters
        )
        
        return SuccessResponse(data=datas, count=count)
    
    async def upload_image(self, file: UploadFile, filename: str = None) -> dict:
        """
        上传图片资源
        """
        # 验证文件类型
        if not file.content_type.startswith('image/'):
            raise CustomException("只能上传图片文件")
        
        # 生成文件名
        if not filename:
            file_ext = os.path.splitext(file.filename)[1]
            filename = f"{uuid.uuid4().hex}{file_ext}"
        
        # 创建上传目录
        upload_dir = os.path.join(STATIC_ROOT, "images")
        os.makedirs(upload_dir, exist_ok=True)
        
        # 保存文件
        file_path = os.path.join(upload_dir, filename)
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # 生成访问URL
        image_url = f"{STATIC_URL}/images/{filename}"
        
        # 保存到数据库
        image_data = {
            "filename": file.filename or filename,
            "image_url": image_url,
            "create_user_id": 1  # 这里应该从当前用户获取
        }
        
        result = await self.image_dal.create_data(image_data)
        return SuccessResponse(data=result, msg="上传成功")
    
    async def delete_images(self, ids: list[int]) -> dict:
        """
        删除图片资源
        """
        # 获取要删除的图片信息
        images = await self.image_dal.get_datas(
            limit=0,
            id=("in", ids),
            v_return_objs=True
        )
        
        # 删除物理文件
        for image in images:
            try:
                # 从URL中提取文件路径
                if image.image_url.startswith(STATIC_URL):
                    relative_path = image.image_url.replace(STATIC_URL, "").lstrip("/")
                    file_path = os.path.join(STATIC_ROOT, relative_path)
                    if os.path.exists(file_path):
                        os.remove(file_path)
            except Exception as e:
                # 文件删除失败不影响数据库删除
                pass
        
        # 删除数据库记录
        await self.image_dal.delete_datas(ids, v_soft=True)
        return SuccessResponse(msg="删除成功")
    
    async def get_image(self, data_id: int) -> dict:
        """
        获取图片资源详情
        """
        data = await self.image_dal.get_data(data_id)
        return SuccessResponse(data=data)
    
    async def update_image(self, data_id: int, data: ImageUpdate) -> dict:
        """
        更新图片资源信息
        """
        await self.image_dal.get_data(data_id)
        result = await self.image_dal.put_data(data_id, data.model_dump(exclude_unset=True))
        return SuccessResponse(data=result, msg="更新成功")
