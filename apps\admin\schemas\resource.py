#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/19
# @File           : resource.py
# @IDE            : PyCharm
# @desc           : 资源管理相关的Pydantic模型

from pydantic import BaseModel, ConfigDict, field_validator
from apps.admin.depts import DatetimeStr
from typing import Optional


# 图片资源相关模型
class ImageBase(BaseModel):
    """图片资源基础模型"""
    filename: str
    image_url: str


class ImageCreate(ImageBase):
    """创建图片资源模型"""
    create_user_id: int


class ImageUpdate(BaseModel):
    """更新图片资源模型"""
    filename: Optional[str] = None


class ImageOut(ImageBase):
    """图片资源输出模型"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    create_user_id: int
    create_datetime: DatetimeStr
    update_datetime: DatetimeStr


class ImageSimpleOut(BaseModel):
    """图片资源简单输出模型"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    filename: str
    image_url: str
    create_datetime: DatetimeStr


# 文件上传相关模型
class FileUploadResponse(BaseModel):
    """文件上传响应模型"""
    filename: str
    file_url: str
    file_size: int
    content_type: str
    upload_time: DatetimeStr


class ImageUploadRequest(BaseModel):
    """图片上传请求模型"""
    path: str = "images"
    
    @field_validator('path')
    @classmethod
    def validate_path(cls, v: str) -> str:
        # 验证路径格式
        allowed_paths = ['images', 'avatars', 'documents', 'temp']
        if v not in allowed_paths:
            raise ValueError(f'路径必须是以下之一: {", ".join(allowed_paths)}')
        return v


class ImageUploadResponse(BaseModel):
    """图片上传响应模型"""
    id: int
    filename: str
    image_url: str
    file_size: int
    content_type: str
    create_datetime: DatetimeStr


# 资源统计相关模型
class ResourceStatsOut(BaseModel):
    """资源统计输出模型"""
    total_images: int
    total_size: int  # 总大小（字节）
    today_uploads: int
    week_uploads: int
    month_uploads: int


class StorageStatsOut(BaseModel):
    """存储统计输出模型"""
    used_space: int  # 已使用空间（字节）
    total_space: int  # 总空间（字节）
    usage_percentage: float  # 使用率百分比
