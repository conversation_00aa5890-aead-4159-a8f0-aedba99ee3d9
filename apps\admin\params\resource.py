#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/19
# @File           : resource.py
# @IDE            : PyCharm
# @desc           : 资源管理相关查询参数

from apps.admin.depts import QueryParams
from typing import Optional


class ImageParams(QueryParams):
    """
    图片资源查询参数
    """
    
    def __init__(
        self,
        filename: Optional[str] = None,
        image_url: Optional[str] = None,
        create_user_id: Optional[int] = None,
        create_datetime_start: Optional[str] = None,
        create_datetime_end: Optional[str] = None,
        params=None
    ):
        super().__init__(params)
        self.filename = filename
        self.image_url = image_url
        self.create_user_id = create_user_id
        self.create_datetime_start = create_datetime_start
        self.create_datetime_end = create_datetime_end
