#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/20
# @File           : user.py
# @IDE            : PyCharm
# @desc           : 用户管理相关API接口

from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from apps.admin.depts import get_admin_db, get_admin_paging, Paging
from apps.admin.services.user import UserService
from apps.admin.schemas.user import UserOut, UserCreate, UserUpdate, UserPasswordUpdate
from apps.admin.params.user import UserParams

app = APIRouter()


@app.get("/users", summary="获取用户列表")
async def get_users(
    params: UserParams = Depends(),
    paging: Paging = get_admin_paging(),
    db: AsyncSession = get_admin_db()
):
    """
    获取用户列表
    """
    service = UserService(db)
    return await service.get_users(params, paging)


@app.post("/users", summary="创建用户")
async def create_user(
    data: UserCreate,
    db: AsyncSession = get_admin_db()
):
    """
    创建用户
    """
    service = UserService(db)
    return await service.create_user(data)


@app.get("/users/{user_id}", summary="获取用户详情")
async def get_user(
    user_id: int,
    db: AsyncSession = get_admin_db()
):
    """
    获取用户详情
    """
    service = UserService(db)
    return await service.get_user(user_id)


@app.put("/users/{user_id}", summary="更新用户")
async def update_user(
    user_id: int,
    data: UserUpdate,
    db: AsyncSession = get_admin_db()
):
    """
    更新用户
    """
    service = UserService(db)
    return await service.update_user(user_id, data)


@app.put("/users/{user_id}/password", summary="修改用户密码")
async def update_user_password(
    user_id: int,
    data: UserPasswordUpdate,
    db: AsyncSession = get_admin_db()
):
    """
    修改用户密码
    """
    service = UserService(db)
    return await service.update_user_password(user_id, data)


@app.delete("/users/{user_id}", summary="删除用户")
async def delete_user(
    user_id: int,
    db: AsyncSession = get_admin_db()
):
    """
    删除用户
    """
    service = UserService(db)
    return await service.delete_user(user_id)


@app.post("/users/{user_id}/roles", summary="分配用户角色")
async def assign_user_roles(
    user_id: int,
    role_ids: list[int],
    db: AsyncSession = get_admin_db()
):
    """
    分配用户角色
    """
    service = UserService(db)
    return await service.assign_user_roles(user_id, role_ids)


@app.post("/users/{user_id}/depts", summary="分配用户部门")
async def assign_user_depts(
    user_id: int,
    dept_ids: list[int],
    db: AsyncSession = get_admin_db()
):
    """
    分配用户部门
    """
    service = UserService(db)
    return await service.assign_user_depts(user_id, dept_ids)


@app.get("/users/{user_id}/roles", summary="获取用户角色")
async def get_user_roles(
    user_id: int,
    db: AsyncSession = get_admin_db()
):
    """
    获取用户角色
    """
    service = UserService(db)
    return await service.get_user_roles(user_id)


@app.get("/users/{user_id}/depts", summary="获取用户部门")
async def get_user_depts(
    user_id: int,
    db: AsyncSession = get_admin_db()
):
    """
    获取用户部门
    """
    service = UserService(db)
    return await service.get_user_depts(user_id)


@app.post("/users/batch-delete", summary="批量删除用户")
async def batch_delete_users(
    user_ids: list[int],
    db: AsyncSession = get_admin_db()
):
    """
    批量删除用户
    """
    service = UserService(db)
    return await service.batch_delete_users(user_ids)


@app.post("/users/{user_id}/enable", summary="启用用户")
async def enable_user(
    user_id: int,
    db: AsyncSession = get_admin_db()
):
    """
    启用用户
    """
    service = UserService(db)
    return await service.enable_user(user_id)


@app.post("/users/{user_id}/disable", summary="禁用用户")
async def disable_user(
    user_id: int,
    db: AsyncSession = get_admin_db()
):
    """
    禁用用户
    """
    service = UserService(db)
    return await service.disable_user(user_id)


@app.post("/users/{user_id}/reset-password", summary="重置用户密码")
async def reset_user_password(
    user_id: int,
    db: AsyncSession = get_admin_db()
):
    """
    重置用户密码为默认密码
    """
    service = UserService(db)
    return await service.reset_user_password(user_id)
