#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/19
# @File           : help.py
# @IDE            : PyCharm
# @desc           : 帮助中心相关查询参数

from apps.admin.depts import QueryParams
from typing import Optional


class IssueCategoryParams(QueryParams):
    """
    问题分类查询参数
    """
    
    def __init__(
        self,
        name: Optional[str] = None,
        platform: Optional[str] = None,
        is_active: Optional[bool] = None,
        create_user_id: Optional[int] = None,
        params=None
    ):
        super().__init__(params)
        self.name = name
        self.platform = platform
        self.is_active = is_active
        self.create_user_id = create_user_id


class IssueParams(QueryParams):
    """
    问题查询参数
    """
    
    def __init__(
        self,
        title: Optional[str] = None,
        category_id: Optional[int] = None,
        is_active: Optional[bool] = None,
        create_user_id: Optional[int] = None,
        view_number_min: Optional[int] = None,
        view_number_max: Optional[int] = None,
        create_datetime_start: Optional[str] = None,
        create_datetime_end: Optional[str] = None,
        params=None
    ):
        super().__init__(params)
        self.title = title
        self.category_id = category_id
        self.is_active = is_active
        self.create_user_id = create_user_id
        self.view_number_min = view_number_min
        self.view_number_max = view_number_max
        self.create_datetime_start = create_datetime_start
        self.create_datetime_end = create_datetime_end
