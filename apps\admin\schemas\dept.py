#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/21
# @File           : dept.py
# @IDE            : PyCharm
# @desc           : 部门管理相关数据模型

from pydantic import BaseModel, ConfigDict, field_validator
from typing import Optional, List
from core.data_types import DatetimeStr, Email


class DeptCreate(BaseModel):
    """部门创建模型"""
    name: str
    dept_key: str
    desc: Optional[str] = None
    disabled: bool = False
    order: Optional[int] = 0
    parent_id: Optional[int] = None
    owner: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[Email] = None

    @field_validator('name')
    @classmethod
    def validate_name(cls, v: str) -> str:
        if not v or len(v.strip()) == 0:
            raise ValueError('部门名称不能为空')
        return v.strip()

    @field_validator('dept_key')
    @classmethod
    def validate_dept_key(cls, v: str) -> str:
        if not v or len(v.strip()) == 0:
            raise ValueError('部门标识不能为空')
        return v.strip()


class DeptUpdate(BaseModel):
    """部门更新模型"""
    name: Optional[str] = None
    dept_key: Optional[str] = None
    desc: Optional[str] = None
    disabled: Optional[bool] = None
    order: Optional[int] = None
    parent_id: Optional[int] = None
    owner: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[Email] = None


class DeptOut(BaseModel):
    """部门输出模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    dept_key: str
    disabled: bool
    desc: Optional[str] = None
    order: Optional[int] = None
    parent_id: Optional[int] = None
    owner: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    create_datetime: DatetimeStr
    update_datetime: Optional[DatetimeStr] = None

    # 子部门
    children: Optional[List["DeptOut"]] = []


class DeptTreeOut(BaseModel):
    """部门树输出模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    dept_key: str
    parent_id: Optional[int] = None
    disabled: bool
    order: Optional[int] = None

    # 子部门
    children: Optional[List["DeptTreeOut"]] = []


class DeptOptionOut(BaseModel):
    """部门选项输出模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    dept_key: str
    parent_id: Optional[int] = None
    disabled: bool


class DeptListOut(BaseModel):
    """部门列表输出模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    dept_key: str
    disabled: bool
    order: Optional[int] = None
    parent_id: Optional[int] = None
    owner: Optional[str] = None
    create_datetime: DatetimeStr


class DeptUserTreeOut(BaseModel):
    """部门用户树输出模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    dept_key: str
    parent_id: Optional[int] = None
    disabled: bool
    type: str = "dept"  # dept 或 user

    # 子部门和用户
    children: Optional[List["DeptUserTreeOut"]] = []


class DeptUserOut(BaseModel):
    """部门用户输出模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    type: str = "user"  # 标识为用户
    telephone: Optional[str] = None
    email: Optional[str] = None
    is_active: bool


class DeptBatchRequest(BaseModel):
    """部门批量操作请求模型"""
    dept_ids: List[int]

    @field_validator('dept_ids')
    @classmethod
    def validate_dept_ids(cls, v: List[int]) -> List[int]:
        if not v or len(v) == 0:
            raise ValueError('部门ID列表不能为空')
        return v


class DeptMoveRequest(BaseModel):
    """部门移动请求模型"""
    target_parent_id: Optional[int] = None
    target_order: Optional[int] = None


class DeptStatsOut(BaseModel):
    """部门统计输出模型"""
    model_config = ConfigDict(from_attributes=True)

    dept_id: int
    dept_name: str
    user_count: int
    active_user_count: int
    sub_dept_count: int
