#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/20
# @File           : auth.py
# @IDE            : PyCharm
# @desc           : 认证相关查询参数（登录、权限验证等）

from apps.admin.depts import QueryParams
from typing import Optional


class UserParams(QueryParams):
    """
    用户查询参数
    """

    def __init__(
        self,
        name: Optional[str] = None,
        telephone: Optional[str] = None,
        email: Optional[str] = None,
        nickname: Optional[str] = None,
        is_active: Optional[bool] = None,
        is_staff: Optional[bool] = None,
        role_id: Optional[int] = None,
        dept_id: Optional[int] = None,
        create_datetime_start: Optional[str] = None,
        create_datetime_end: Optional[str] = None,
        params=None
    ):
        super().__init__(params)
        self.name = name
        self.telephone = telephone
        self.email = email
        self.nickname = nickname
        self.is_active = is_active
        self.is_staff = is_staff
        self.role_id = role_id
        self.dept_id = dept_id
        self.create_datetime_start = create_datetime_start
        self.create_datetime_end = create_datetime_end


class LoginParams(QueryParams):
    """
    登录相关查询参数
    """

    def __init__(
        self,
        login_datetime_start: Optional[str] = None,
        login_datetime_end: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        login_status: Optional[str] = None,  # success, failed
        params=None
    ):
        super().__init__(params)
        self.login_datetime_start = login_datetime_start
        self.login_datetime_end = login_datetime_end
        self.ip_address = ip_address
        self.user_agent = user_agent
        self.login_status = login_status


class PermissionParams(QueryParams):
    """
    权限相关查询参数
    """

    def __init__(
        self,
        permission_key: Optional[str] = None,
        menu_id: Optional[int] = None,
        role_id: Optional[int] = None,
        params=None
    ):
        super().__init__(params)
        self.permission_key = permission_key
        self.menu_id = menu_id
        self.role_id = role_id


class RoleParams(QueryParams):
    """
    角色查询参数
    """
    
    def __init__(
        self,
        name: Optional[str] = None,
        role_key: Optional[str] = None,
        disabled: Optional[bool] = None,
        is_admin: Optional[bool] = None,
        params=None
    ):
        super().__init__(params)
        self.name = name
        self.role_key = role_key
        self.disabled = disabled
        self.is_admin = is_admin


class MenuParams(QueryParams):
    """
    菜单查询参数
    """
    
    def __init__(
        self,
        title: Optional[str] = None,
        menu_type: Optional[str] = None,
        disabled: Optional[bool] = None,
        hidden: Optional[bool] = None,
        parent_id: Optional[int] = None,
        params=None
    ):
        super().__init__(params)
        self.title = title
        self.menu_type = menu_type
        self.disabled = disabled
        self.hidden = hidden
        self.parent_id = parent_id


class DeptParams(QueryParams):
    """
    部门查询参数
    """
    
    def __init__(
        self,
        name: Optional[str] = None,
        dept_key: Optional[str] = None,
        disabled: Optional[bool] = None,
        parent_id: Optional[int] = None,
        params=None
    ):
        super().__init__(params)
        self.name = name
        self.dept_key = dept_key
        self.disabled = disabled
        self.parent_id = parent_id
