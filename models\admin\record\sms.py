#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2022/3/21 17:36 
# @File           : sms.py
# @IDE            : PyCharm
# @desc           : 短信发送记录模型

from datetime import datetime
from sqlalchemy.orm import Mapped, mapped_column
from models.base import BaseModel
from sqlalchemy import Integer, String, Boolean, ForeignKey, DateTime, func


class AdminSMSSendRecord(BaseModel):
    __tablename__ = "admin_record_sms_send"
    __table_args__ = ({'comment': '短信发送记录表'})

    # 基础字段
    id: Mapped[int] = mapped_column(Integer, primary_key=True, comment='主键ID')
    create_datetime: Mapped[datetime] = mapped_column(DateTime, server_default=func.now(), comment='创建时间')
    update_datetime: Mapped[datetime] = mapped_column(
        DateTime,
        server_default=func.now(),
        onupdate=func.now(),
        comment='更新时间'
    )
    delete_datetime: Mapped[datetime | None] = mapped_column(DateTime, nullable=True, comment='删除时间')
    is_delete: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否软删除")

    # 业务字段
    user_id: Mapped[int] = mapped_column(Integer, ForeignKey("admin_auth_user.id", ondelete='CASCADE'), comment="操作人")
    status: Mapped[bool] = mapped_column(Boolean, default=True, comment="发送状态")
    content: Mapped[str] = mapped_column(String(255), comment="发送内容")
    telephone: Mapped[str] = mapped_column(String(11), comment="目标手机号")
    desc: Mapped[str | None] = mapped_column(String(255), comment="失败描述")
    scene: Mapped[str | None] = mapped_column(String(50), comment="发送场景")
