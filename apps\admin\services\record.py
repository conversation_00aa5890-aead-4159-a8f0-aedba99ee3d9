#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/19
# @File           : record.py
# @IDE            : PyCharm
# @desc           : 记录管理相关服务

from sqlalchemy.ext.asyncio import AsyncSession
from motor.motor_asyncio import AsyncIOMotorDatabase
from apps.admin.depts import AdminService
from models.admin.record.login import AdminLoginRecord
from models.admin.record.sms import AdminSMSSendRecord
from apps.admin.schemas.record import (
    LoginRecordOut, SMSRecordOut, OperationRecordOut
)
from apps.admin.params.record import (
    LoginRecordParams, SMSRecordParams, OperationRecordParams
)
from apps.admin.depts import Paging, SuccessResponse, MongoManage
from typing import Any


class RecordService(AdminService):
    """
    记录管理相关服务类
    """
    
    def __init__(self, db: AsyncSession = None, mongo_db: AsyncIOMotorDatabase = None):
        if db:
            super().__init__(db)
            self.login_record_dal = self.get_dal(AdminLoginRecord, LoginRecordOut)
            self.sms_record_dal = self.get_dal(AdminSMSSendRecord, SMSRecordOut)
        
        if mongo_db:
            self.mongo_db = mongo_db
            self.operation_record_dal = MongoManage(
                mongo_db, 
                "operation_record", 
                OperationRecordOut
            )
    
    # 登录记录管理
    async def get_login_records(self, params: LoginRecordParams, paging: Paging) -> dict:
        """
        获取登录记录列表
        """
        filters = {}
        if params.telephone:
            filters["telephone"] = ("like", params.telephone)
        if params.status is not None:
            filters["status"] = params.status
        if params.platform:
            filters["platform"] = params.platform
        if params.login_method:
            filters["login_method"] = params.login_method
        if params.ip:
            filters["ip"] = ("like", params.ip)
        if params.address:
            filters["address"] = ("like", params.address)
        if params.create_datetime_start:
            filters["create_datetime"] = (">=", params.create_datetime_start)
        if params.create_datetime_end:
            filters["create_datetime"] = ("<=", params.create_datetime_end)
        
        datas, count = await self.login_record_dal.get_datas(
            page=paging.page,
            limit=paging.limit,
            v_return_count=True,
            v_order=paging.v_order,
            v_order_field=paging.v_order_field,
            **filters
        )
        
        return SuccessResponse(data=datas, count=count)
    
    # 短信记录管理
    async def get_sms_records(self, params: SMSRecordParams, paging: Paging) -> dict:
        """
        获取短信记录列表
        """
        filters = {}
        if params.telephone:
            filters["telephone"] = ("like", params.telephone)
        if params.status is not None:
            filters["status"] = params.status
        if params.content:
            filters["content"] = ("like", params.content)
        if params.scene:
            filters["scene"] = params.scene
        if params.user_id:
            filters["user_id"] = params.user_id
        if params.create_datetime_start:
            filters["create_datetime"] = (">=", params.create_datetime_start)
        if params.create_datetime_end:
            filters["create_datetime"] = ("<=", params.create_datetime_end)
        
        datas, count = await self.sms_record_dal.get_datas(
            page=paging.page,
            limit=paging.limit,
            v_return_count=True,
            v_order=paging.v_order,
            v_order_field=paging.v_order_field,
            **filters
        )
        
        return SuccessResponse(data=datas, count=count)
    
    # 操作记录管理（MongoDB）
    async def get_operation_records(self, params: OperationRecordParams, paging: Paging) -> dict:
        """
        获取操作记录列表
        """
        filters = {}
        if params.user_id:
            filters["user_id"] = params.user_id
        if params.method:
            filters["method"] = params.method
        if params.path:
            filters["path"] = params.path
        if params.ip:
            filters["ip"] = params.ip
        if params.status_code:
            filters["status_code"] = params.status_code
        if params.create_datetime_start:
            filters["create_datetime"] = (">=", params.create_datetime_start)
        if params.create_datetime_end:
            filters["create_datetime"] = ("<=", params.create_datetime_end)
        
        # MongoDB分页查询
        skip = (paging.page - 1) * paging.limit
        cursor = self.operation_record_dal.collection.find(filters).skip(skip).limit(paging.limit)
        
        if paging.v_order == "desc":
            cursor = cursor.sort("create_datetime", -1)
        else:
            cursor = cursor.sort("create_datetime", 1)
        
        datas = []
        async for doc in cursor:
            datas.append(doc)
        
        # 获取总数
        count = await self.operation_record_dal.collection.count_documents(filters)
        
        return SuccessResponse(data=datas, count=count)
    
    # 统计分析
    async def get_user_login_distribute(self) -> dict:
        """
        获取用户登录分布情况
        """
        # 这里需要实现具体的统计逻辑
        # 暂时返回模拟数据
        result = [
            {"date": "2025-01-01", "success_count": 100, "fail_count": 10, "total_count": 110},
            {"date": "2025-01-02", "success_count": 120, "fail_count": 8, "total_count": 128},
            {"date": "2025-01-03", "success_count": 90, "fail_count": 15, "total_count": 105},
        ]
        return SuccessResponse(data=result)
    
    async def get_user_login_latest(self) -> dict:
        """
        获取最新用户登录列表
        """
        datas = await self.login_record_dal.get_datas(
            limit=10,
            v_order="desc",
            v_order_field="create_datetime"
        )
        return SuccessResponse(data=datas)
    
    async def get_user_login_count(self) -> dict:
        """
        获取用户登录统计
        """
        # 这里需要实现具体的统计逻辑
        # 暂时返回模拟数据
        result = {
            "today_count": 50,
            "yesterday_count": 45,
            "week_count": 300,
            "month_count": 1200,
            "total_count": 5000
        }
        return SuccessResponse(data=result)
