#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2022/7/7 13:41
# @File           : m2m.py
# @IDE            : PyCharm
# @desc           : 多对多关系表

from sqlalchemy import Table, Column, Integer, Foreign<PERSON>ey
from core.database import Base

# 用户角色关联表
admin_auth_user_roles = Table(
    'admin_auth_user_roles',
    Base.metadata,
    Column('user_id', Integer, Foreign<PERSON>ey('admin_auth_user.id'), primary_key=True),
    Column('role_id', Integer, ForeignKey('admin_auth_role.id'), primary_key=True)
)

# 角色菜单关联表
admin_auth_role_menus = Table(
    'admin_auth_role_menus',
    Base.metadata,
    Column('role_id', Integer, ForeignKey('admin_auth_role.id'), primary_key=True),
    Column('menu_id', Integer, Foreign<PERSON>ey('admin_auth_menu.id'), primary_key=True)
)

# 用户部门关联表
admin_auth_user_depts = Table(
    'admin_auth_user_depts',
    Base.metadata,
    Column('user_id', Integer, Foreign<PERSON>ey('admin_auth_user.id'), primary_key=True),
    Column('dept_id', Integer, ForeignKey('admin_auth_dept.id'), primary_key=True)
)

# 角色部门关联表
admin_auth_role_depts = Table(
    'admin_auth_role_depts',
    Base.metadata,
    Column('role_id', Integer, ForeignKey('admin_auth_role.id'), primary_key=True),
    Column('dept_id', Integer, ForeignKey('admin_auth_dept.id'), primary_key=True)
)
