# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/20
# @File           : task_scheduler.py
# @IDE            : PyCharm
# @desc           : 定时任务调度器核心（合并了任务管理器和监听器功能）

import asyncio
import datetime
import importlib
import json
import re
from typing import List, Optional, Union
from apscheduler.jobstores.base import JobLookupError, ConflictingIdError
from apscheduler.jobstores.mongodb import MongoDBJobStore
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.date import DateTrigger
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.job import Job
from apscheduler.events import EVENT_JOB_EXECUTED, JobExecutionEvent
from core.logger import logger
import pytz


class TaskScheduler:
    """
    定时任务调度器
    基于APScheduler实现的任务调度管理器
    合并了任务管理器和监听器功能，提供完整的任务管理解决方案
    """

    def __init__(self, mongo_db_name: str = None, collection: str = "scheduler_task_jobs",
                 task_dir: str = "tasks"):
        self.scheduler: Optional[BackgroundScheduler] = None
        self.mongo_db_name = mongo_db_name
        self.collection = collection
        self.task_dir = task_dir
        self.db = None
        # 任务管理器相关属性
        self.redis_listener_task: Optional[asyncio.Task] = None
        self.is_running = False

    def start(self, listener: bool = True) -> None:
        """
        启动调度器
        :param listener: 是否注册事件监听器
        """
        self.scheduler = BackgroundScheduler()

        if listener:
            # 注册内置的事件监听器
            self.scheduler.add_listener(self.task_execution_listener, EVENT_JOB_EXECUTED)

        # 如果配置了MongoDB，则添加MongoDB作为任务存储
        if self.mongo_db_name:
            self.scheduler.add_jobstore(self._get_mongodb_job_store())

        self.scheduler.start()
        self.is_running = True
        logger.info("任务调度器启动成功")

    async def start_manager(self):
        """
        启动任务管理器（异步版本，包含Redis监听等功能）
        """
        try:
            # 初始化调度器配置
            mongo_db_name = None
            from config.setting import MONGO_DB_ENABLE, REDIS_DB_ENABLE
            if MONGO_DB_ENABLE:
                from config.env.pro import MONGO_DB_NAME
                mongo_db_name = MONGO_DB_NAME

            # 更新配置
            if mongo_db_name:
                self.mongo_db_name = mongo_db_name

            # 启动调度器
            self.start()

            # 启动Redis监听器（如果启用了Redis）
            if REDIS_DB_ENABLE:
                self.redis_listener_task = asyncio.create_task(self._start_redis_listener())

            # 同步数据库中的任务到调度器
            await self._sync_tasks_from_database()

            logger.info("任务管理器启动成功")

        except Exception as e:
            logger.error(f"任务管理器启动失败: {e}")
            raise

    def _get_mongodb_job_store(self) -> MongoDBJobStore:
        """
        获取MongoDB任务存储
        """
        from core.database import mongo_getter
        # 这里需要获取MongoDB客户端，暂时使用简化实现
        # 实际使用时需要根据具体的MongoDB配置来实现
        return MongoDBJobStore(database=self.mongo_db_name, collection=self.collection)

    def add_job(
        self,
        job_class: str,
        trigger: Union[CronTrigger, DateTrigger, IntervalTrigger],
        name: str = None,
        *args,
        **kwargs
    ) -> Optional[Job]:
        """
        添加任务
        :param job_class: 任务类路径
        :param trigger: 触发器
        :param name: 任务名称
        :return: Job对象
        """
        try:
            job_instance = self._import_module(job_class)
            if job_instance:
                return self.scheduler.add_job(
                    job_instance.main, 
                    trigger=trigger, 
                    args=args, 
                    kwargs=kwargs, 
                    id=name
                )
            else:
                raise ValueError(f"添加任务失败，未找到该模块下的方法：{job_class}")
        except ConflictingIdError:
            raise ValueError("任务编号已存在")
        except Exception as e:
            logger.error(f"添加任务失败: {e}")
            raise

    def add_cron_job(
        self,
        job_class: str,
        expression: str,
        start_date: str = None,
        end_date: str = None,
        timezone: str = "Asia/Shanghai",
        name: str = None,
        args: tuple = (),
        **kwargs
    ) -> Optional[Job]:
        """
        添加Cron定时任务
        :param job_class: 任务类路径
        :param expression: Cron表达式
        :param start_date: 开始时间
        :param end_date: 结束时间
        :param timezone: 时区
        :param name: 任务名称
        :param args: 参数
        :return: Job对象
        """
        second, minute, hour, day, month, day_of_week, year = self._parse_cron_expression(expression)
        trigger = CronTrigger(
            second=second,
            minute=minute,
            hour=hour,
            day=day,
            month=month,
            day_of_week=day_of_week,
            year=year,
            start_date=start_date,
            end_date=end_date,
            timezone=timezone
        )
        return self.add_job(job_class, trigger, name, *args, **kwargs)

    def add_date_job(
        self, 
        job_class: str, 
        expression: Union[str, datetime.datetime], 
        name: str = None, 
        args: tuple = (), 
        **kwargs
    ) -> Optional[Job]:
        """
        添加指定时间执行的任务
        :param job_class: 任务类路径
        :param expression: 执行时间
        :param name: 任务名称
        :param args: 参数
        :return: Job对象
        """
        trigger = DateTrigger(run_date=expression)
        return self.add_job(job_class, trigger, name, *args, **kwargs)

    def add_interval_job(
        self,
        job_class: str,
        expression: str,
        start_date: Union[str, datetime.datetime] = None,
        end_date: Union[str, datetime.datetime] = None,
        timezone: str = "Asia/Shanghai",
        jitter: int = None,
        name: str = None,
        args: tuple = (),
        **kwargs
    ) -> Optional[Job]:
        """
        添加间隔执行的任务
        :param job_class: 任务类路径
        :param expression: 间隔表达式
        :param start_date: 开始时间
        :param end_date: 结束时间
        :param timezone: 时区
        :param jitter: 时间抖动
        :param name: 任务名称
        :param args: 参数
        :return: Job对象
        """
        second, minute, hour, day, week = self._parse_interval_expression(expression)
        trigger = IntervalTrigger(
            weeks=week,
            days=day,
            hours=hour,
            minutes=minute,
            seconds=second,
            start_date=start_date,
            end_date=end_date,
            timezone=timezone,
            jitter=jitter
        )
        return self.add_job(job_class, trigger, name, *args, **kwargs)

    def remove_job(self, name: str) -> None:
        """
        删除任务
        :param name: 任务名称
        """
        try:
            self.scheduler.remove_job(name)
        except JobLookupError as e:
            raise ValueError(f"删除任务失败: {e}")

    def get_job(self, name: str) -> Optional[Job]:
        """
        获取任务
        :param name: 任务名称
        :return: Job对象
        """
        return self.scheduler.get_job(name)

    def get_jobs(self) -> List[Job]:
        """
        获取所有任务
        :return: Job列表
        """
        return self.scheduler.get_jobs()

    def has_job(self, name: str) -> bool:
        """
        判断任务是否存在
        :param name: 任务名称
        :return: 是否存在
        """
        return self.get_job(name) is not None

    def run_job(self, job_class: str, args: tuple = (), **kwargs) -> None:
        """
        立即执行任务（不通过调度器）
        :param job_class: 任务类路径
        :param args: 参数
        """
        job_instance = self._import_module(job_class)
        if job_instance:
            job_instance.main(*args, **kwargs)

    def shutdown(self) -> None:
        """
        关闭调度器
        """
        if self.scheduler:
            self.scheduler.shutdown()
            logger.info("任务调度器已关闭")

    async def stop_manager(self):
        """
        停止任务管理器（异步版本）
        """
        try:
            self.is_running = False

            # 停止Redis监听器
            if self.redis_listener_task:
                self.redis_listener_task.cancel()
                try:
                    await self.redis_listener_task
                except asyncio.CancelledError:
                    pass

            # 停止调度器
            self.shutdown()

            logger.info("任务管理器已停止")

        except Exception as e:
            logger.error(f"停止任务管理器失败: {e}")

    async def _sync_tasks_from_database(self):
        """
        从数据库同步任务到调度器
        """
        try:
            from core.database import db_getter
            from models.admin.system.task import AdminTask
            from sqlalchemy import select

            # 这里需要异步数据库操作，暂时简化实现
            # 实际使用时需要根据具体的数据库配置来完善
            logger.info("开始同步数据库中的任务到调度器")

            # TODO: 实现从数据库加载启用的任务并添加到调度器

        except Exception as e:
            logger.error(f"同步任务失败: {e}")

    async def _start_redis_listener(self):
        """
        启动Redis监听器
        """
        try:
            from config.setting import REDIS_DB_ENABLE
            if not REDIS_DB_ENABLE:
                return

            # 这里需要实现Redis监听逻辑
            # 暂时简化实现，实际使用时需要完善
            logger.info("Redis监听器启动成功")

            while self.is_running:
                await asyncio.sleep(1)

        except Exception as e:
            logger.error(f"Redis监听器异常: {e}")

    def task_execution_listener(self, event: JobExecutionEvent):
        """
        任务执行监听器
        在任务执行完成后记录执行日志
        """
        try:
            # 获取时区
            shanghai_tz = pytz.timezone("Asia/Shanghai")
            start_time: datetime.datetime = event.scheduled_run_time.astimezone(shanghai_tz)
            end_time = datetime.datetime.now(shanghai_tz)
            process_time = (end_time - start_time).total_seconds()

            # 处理任务ID（去除临时任务的随机后缀）
            job_id = event.job_id
            if "-temp-" in job_id:
                job_id = job_id.split("-")[0]

            # 构建执行记录
            result = {
                "job_id": job_id,
                "start_time": start_time.strftime("%Y-%m-%d %H:%M:%S"),
                "end_time": end_time.strftime("%Y-%m-%d %H:%M:%S"),
                "process_time": process_time,
                "retval": json.dumps(event.retval) if event.retval else None,
                "exception": json.dumps(str(event.exception)) if event.exception else None,
                "traceback": json.dumps(event.traceback) if event.traceback else None
            }

            # 尝试获取任务详细信息
            try:
                # 这里需要异步处理，暂时记录基本信息
                # 实际实现时需要根据具体的数据库操作来完善
                logger.info(f"任务执行完成: {job_id}, 耗时: {process_time}秒")

                # 保存执行记录到MongoDB
                self._save_task_record(result)

            except Exception as e:
                logger.error(f"获取任务详细信息失败: {e}")
                result["exception"] = str(e)
                self._save_task_record(result)

        except Exception as e:
            logger.error(f"任务执行监听器异常: {e}")

    def _save_task_record(self, record: dict):
        """
        保存任务执行记录到MongoDB
        """
        try:
            from core.mongo_manage import MongoManage
            from config.setting import MONGO_DB_ENABLE

            if MONGO_DB_ENABLE:
                # 这里需要获取MongoDB连接并保存记录
                # 暂时只记录日志，实际实现时需要完善
                logger.info(f"保存任务执行记录: {record['job_id']}")

        except Exception as e:
            logger.error(f"保存任务执行记录失败: {e}")

    def before_job_execution(self, event):
        """
        任务执行前的监听器（兼容原有接口）
        """
        self.task_execution_listener(event)

    def _import_module(self, expression: str):
        """
        动态导入模块
        :param expression: 类路径表达式
        :return: 类实例
        """
        module, args = self._parse_string_to_class(expression)
        module_path = f"{self.task_dir}.{module[:module.rindex('.')]}"
        module_class = module[module.rindex('.') + 1:]
        
        try:
            # 动态导入模块
            pag = importlib.import_module(module_path)
            return getattr(pag, module_class)(*args)
        except ModuleNotFoundError:
            raise ValueError(f"未找到该模块：{module_path}")
        except AttributeError:
            raise ValueError(f"未找到该模块下的方法：{module_class}")
        except TypeError as e:
            raise ValueError(f"参数传递错误：{args}, 详情：{e}")

    @staticmethod
    def _parse_cron_expression(expression: str) -> tuple:
        """
        解析Cron表达式
        :param expression: Cron表达式
        :return: 解析后的字段元组
        """
        fields = expression.strip().split()
        
        if len(fields) not in (6, 7):
            raise ValueError("无效的Cron表达式")
        
        parsed_fields = [None if field in ('*', '?') else field for field in fields]
        if len(fields) == 6:
            parsed_fields.append(None)
        
        return tuple(parsed_fields)

    @staticmethod
    def _parse_interval_expression(expression: str) -> tuple:
        """
        解析间隔表达式
        :param expression: 间隔表达式 (秒 分 时 天 周)
        :return: 解析后的字段元组
        """
        fields = expression.strip().split()
        
        if len(fields) != 5:
            raise ValueError("无效的间隔表达式")
        
        parsed_fields = [int(field) if field != '*' else 0 for field in fields]
        return tuple(parsed_fields)

    @classmethod
    def _parse_string_to_class(cls, expression: str) -> tuple:
        """
        解析类路径和参数
        :param expression: 表达式
        :return: (类路径, 参数列表)
        """
        pattern = r'([\w.]+)(?:\((.*)\))?'
        match = re.match(pattern, expression)
        
        if match:
            class_path = match.group(1)
            arguments = match.group(2)
            
            if arguments:
                arguments = cls._parse_arguments(arguments)
            else:
                arguments = []
            
            return class_path, arguments
        
        return None, None

    @staticmethod
    def _parse_arguments(args_str: str) -> list:
        """
        解析参数字符串
        :param args_str: 参数字符串
        :return: 参数列表
        """
        arguments = []
        
        for arg in re.findall(r'"([^"]*)"|(\d+\.\d+)|(\d+)|([Tt]rue|[Ff]alse)', args_str):
            if arg[0]:
                # 字符串参数
                arguments.append(arg[0])
            elif arg[1]:
                # 浮点数参数
                arguments.append(float(arg[1]))
            elif arg[2]:
                # 整数参数
                arguments.append(int(arg[2]))
            elif arg[3]:
                # 布尔参数
                arguments.append(arg[3].lower() == 'true')
        
        return arguments


# 全局调度器实例
_scheduler_instance: Optional[TaskScheduler] = None


def get_scheduler() -> TaskScheduler:
    """
    获取全局调度器实例
    """
    global _scheduler_instance
    if _scheduler_instance is None:
        _scheduler_instance = TaskScheduler()
    return _scheduler_instance


def init_scheduler(mongo_db_name: str = None, collection: str = "scheduler_task_jobs",
                  task_dir: str = "tasks") -> TaskScheduler:
    """
    初始化调度器
    """
    global _scheduler_instance
    _scheduler_instance = TaskScheduler(mongo_db_name, collection, task_dir)
    return _scheduler_instance


# 任务管理器相关的全局函数（兼容原有接口）
_task_manager_instance: Optional[TaskScheduler] = None


def get_task_manager() -> TaskScheduler:
    """
    获取全局任务管理器实例（实际返回TaskScheduler实例）
    """
    global _task_manager_instance
    if _task_manager_instance is None:
        _task_manager_instance = TaskScheduler()
    return _task_manager_instance


async def init_task_manager():
    """
    初始化任务管理器
    """
    manager = get_task_manager()
    await manager.start_manager()
    return manager


async def shutdown_task_manager():
    """
    关闭任务管理器
    """
    global _task_manager_instance
    if _task_manager_instance:
        await _task_manager_instance.stop_manager()
        _task_manager_instance = None


# 兼容性别名
TaskManager = TaskScheduler
