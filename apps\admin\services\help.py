#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/19
# @File           : help.py
# @IDE            : PyCharm
# @desc           : 帮助中心相关服务

from sqlalchemy.ext.asyncio import AsyncSession
from apps.admin.depts import AdminService
from models.admin.help.issue import AdminIssue, AdminIssueCategory
from apps.admin.schemas.help import (
    IssueCategoryOut, IssueCategoryCreate, IssueCategoryUpdate,
    IssueOut, IssueCreate, IssueUpdate
)
from apps.admin.params.help import (
    IssueCategoryParams, IssueParams
)
from apps.admin.depts import Paging, CustomException, SuccessResponse
from typing import Any


class HelpService(AdminService):
    """
    帮助中心相关服务类
    """
    
    def __init__(self, db: AsyncSession):
        super().__init__(db)
        self.issue_category_dal = self.get_dal(AdminIssueCategory, IssueCategoryOut)
        self.issue_dal = self.get_dal(AdminIssue, IssueOut)
    
    # 问题分类管理
    async def get_issue_categories(self, params: IssueCategoryParams, paging: Paging) -> dict:
        """
        获取问题分类列表
        """
        filters = {}
        if params.name:
            filters["name"] = ("like", params.name)
        if params.platform:
            filters["platform"] = params.platform
        if params.is_active is not None:
            filters["is_active"] = params.is_active
        if params.create_user_id:
            filters["create_user_id"] = params.create_user_id
        
        datas, count = await self.issue_category_dal.get_datas(
            page=paging.page,
            limit=paging.limit,
            v_return_count=True,
            v_order=paging.v_order,
            v_order_field=paging.v_order_field,
            **filters
        )
        
        return SuccessResponse(data=datas, count=count)
    
    async def create_issue_category(self, data: IssueCategoryCreate) -> dict:
        """
        创建问题分类
        """
        result = await self.issue_category_dal.create_data(data.model_dump())
        return SuccessResponse(data=result, msg="创建成功")
    
    async def update_issue_category(self, data_id: int, data: IssueCategoryUpdate) -> dict:
        """
        更新问题分类
        """
        await self.issue_category_dal.get_data(data_id)
        result = await self.issue_category_dal.put_data(data_id, data.model_dump(exclude_unset=True))
        return SuccessResponse(data=result, msg="更新成功")
    
    async def delete_issue_categories(self, ids: list[int]) -> dict:
        """
        删除问题分类
        """
        # 检查是否有关联的问题
        for category_id in ids:
            issue_count = await self.issue_dal.get_count(category_id=category_id)
            if issue_count > 0:
                raise CustomException(f"分类ID {category_id} 下还有问题，无法删除")
        
        await self.issue_category_dal.delete_datas(ids, v_soft=True)
        return SuccessResponse(msg="删除成功")
    
    async def get_issue_category(self, data_id: int) -> dict:
        """
        获取问题分类详情
        """
        data = await self.issue_category_dal.get_data(data_id)
        return SuccessResponse(data=data)
    
    # 问题管理
    async def get_issues(self, params: IssueParams, paging: Paging) -> dict:
        """
        获取问题列表
        """
        filters = {}
        if params.title:
            filters["title"] = ("like", params.title)
        if params.category_id:
            filters["category_id"] = params.category_id
        if params.is_active is not None:
            filters["is_active"] = params.is_active
        if params.create_user_id:
            filters["create_user_id"] = params.create_user_id
        if params.view_number_min is not None:
            filters["view_number"] = (">=", params.view_number_min)
        if params.view_number_max is not None:
            filters["view_number"] = ("<=", params.view_number_max)
        if params.create_datetime_start:
            filters["create_datetime"] = (">=", params.create_datetime_start)
        if params.create_datetime_end:
            filters["create_datetime"] = ("<=", params.create_datetime_end)
        
        datas, count = await self.issue_dal.get_datas(
            page=paging.page,
            limit=paging.limit,
            v_return_count=True,
            v_order=paging.v_order,
            v_order_field=paging.v_order_field,
            **filters
        )
        
        return SuccessResponse(data=datas, count=count)
    
    async def create_issue(self, data: IssueCreate) -> dict:
        """
        创建问题
        """
        # 检查分类是否存在
        await self.issue_category_dal.get_data(data.category_id)
        
        result = await self.issue_dal.create_data(data.model_dump())
        return SuccessResponse(data=result, msg="创建成功")
    
    async def update_issue(self, data_id: int, data: IssueUpdate) -> dict:
        """
        更新问题
        """
        await self.issue_dal.get_data(data_id)
        
        # 如果更新了分类，检查分类是否存在
        if data.category_id:
            await self.issue_category_dal.get_data(data.category_id)
        
        result = await self.issue_dal.put_data(data_id, data.model_dump(exclude_unset=True))
        return SuccessResponse(data=result, msg="更新成功")
    
    async def delete_issues(self, ids: list[int]) -> dict:
        """
        删除问题
        """
        await self.issue_dal.delete_datas(ids, v_soft=True)
        return SuccessResponse(msg="删除成功")
    
    async def get_issue(self, data_id: int) -> dict:
        """
        获取问题详情
        """
        data = await self.issue_dal.get_data(data_id)
        return SuccessResponse(data=data)
    
    async def increase_issue_view(self, data_id: int) -> dict:
        """
        增加问题查看次数
        """
        issue = await self.issue_dal.get_data(data_id)
        new_view_number = issue.view_number + 1
        await self.issue_dal.put_data(data_id, {"view_number": new_view_number})
        return SuccessResponse(msg="查看次数已增加")
