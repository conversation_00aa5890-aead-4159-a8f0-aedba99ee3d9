#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/20
# @File           : user.py
# @IDE            : PyCharm
# @desc           : 用户管理相关的Pydantic模型

from pydantic import BaseModel, ConfigDict, field_validator
from pydantic_core.core_schema import FieldValidationInfo
from apps.admin.depts import DatetimeStr, Telephone, Email
from typing import Optional, List


# 用户相关模型
class UserBase(BaseModel):
    """用户基础模型"""
    name: str
    telephone: Telephone
    email: Optional[Email] = None
    nickname: Optional[str] = None
    avatar: Optional[str] = None
    is_active: Optional[bool] = True
    is_staff: Optional[bool] = True
    gender: Optional[str] = "0"


class UserCreate(UserBase):
    """创建用户模型"""
    password: str
    role_ids: Optional[List[int]] = []
    dept_ids: Optional[List[int]] = []
    
    @field_validator('password')
    @classmethod
    def validate_password(cls, v: str, info: FieldValidationInfo) -> str:
        if len(v) < 6:
            raise ValueError('密码长度不能少于6位')
        return v


class UserUpdate(BaseModel):
    """更新用户模型"""
    name: Optional[str] = None
    telephone: Optional[Telephone] = None
    email: Optional[Email] = None
    nickname: Optional[str] = None
    avatar: Optional[str] = None
    is_active: Optional[bool] = None
    is_staff: Optional[bool] = None
    gender: Optional[str] = None
    role_ids: Optional[List[int]] = None
    dept_ids: Optional[List[int]] = None


class UserPasswordUpdate(BaseModel):
    """用户密码更新模型"""
    old_password: str
    new_password: str
    confirm_password: str
    
    @field_validator('new_password')
    @classmethod
    def validate_new_password(cls, v: str, info: FieldValidationInfo) -> str:
        if len(v) < 6:
            raise ValueError('新密码长度不能少于6位')
        return v
    
    @field_validator('confirm_password')
    @classmethod
    def validate_confirm_password(cls, v: str, info: FieldValidationInfo) -> str:
        if 'new_password' in info.data and v != info.data['new_password']:
            raise ValueError('确认密码与新密码不一致')
        return v


class UserOut(UserBase):
    """用户输出模型"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    last_ip: Optional[str] = None
    last_login: Optional[DatetimeStr] = None
    is_reset_password: bool
    wx_server_openid: Optional[str] = None
    is_wx_server_openid: bool
    create_datetime: DatetimeStr
    update_datetime: DatetimeStr
    
    # 关联数据
    roles: Optional[List["RoleSimpleOut"]] = []
    depts: Optional[List["DeptSimpleOut"]] = []


class UserSimpleOut(BaseModel):
    """用户简单输出模型"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    name: str
    telephone: str
    email: Optional[str] = None
    nickname: Optional[str] = None
    avatar: Optional[str] = None
    is_active: bool
    is_staff: bool


class UserListOut(BaseModel):
    """用户列表输出模型"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    name: str
    telephone: str
    email: Optional[str] = None
    nickname: Optional[str] = None
    avatar: Optional[str] = None
    is_active: bool
    is_staff: bool
    gender: Optional[str] = None
    last_ip: Optional[str] = None
    last_login: Optional[DatetimeStr] = None
    create_datetime: DatetimeStr
    
    # 关联数据
    roles: Optional[List["RoleSimpleOut"]] = []
    depts: Optional[List["DeptSimpleOut"]] = []


# 角色简单输出模型（用于用户关联）
class RoleSimpleOut(BaseModel):
    """角色简单输出模型"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    name: str
    role_key: str
    disabled: bool


# 部门简单输出模型（用于用户关联）
class DeptSimpleOut(BaseModel):
    """部门简单输出模型"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    name: str
    dept_key: str
    disabled: bool


# 用户角色分配模型
class UserRoleAssign(BaseModel):
    """用户角色分配模型"""
    user_id: int
    role_ids: List[int]


# 用户部门分配模型
class UserDeptAssign(BaseModel):
    """用户部门分配模型"""
    user_id: int
    dept_ids: List[int]


# 批量操作模型
class UserBatchDelete(BaseModel):
    """用户批量删除模型"""
    user_ids: List[int]


# 用户状态更新模型
class UserStatusUpdate(BaseModel):
    """用户状态更新模型"""
    is_active: bool


# 用户密码重置模型
class UserPasswordReset(BaseModel):
    """用户密码重置模型"""
    new_password: Optional[str] = "123456"  # 默认密码
