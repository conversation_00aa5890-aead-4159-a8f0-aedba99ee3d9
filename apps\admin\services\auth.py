#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/20
# @File           : auth.py
# @IDE            : PyCharm
# @desc           : 认证相关服务（登录、登出、权限验证等）

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_
from fastapi import Request
from apps.admin.depts import AdminService
from models.admin.auth.user import AdminUser
from models.admin.auth.role import AdminRole
from models.admin.auth.dept import AdminDept
from models.admin.auth.menu import AdminMenu
from models.admin.record.login import AdminLoginRecord
from apps.admin.schemas.auth import (
    LoginRequest, LoginResponse, RefreshTokenRequest, RefreshTokenResponse,
    ChangePasswordRequest, UserProfileOut, UserProfileUpdate,
    TokenVerifyResponse, CaptchaResponse, LogoutResponse,
    MenuOut, PermissionOut, RoleOut, DeptOut
)
from apps.admin.depts import CustomException, SuccessResponse
from passlib.context import Crypt<PERSON>ontext
from datetime import datetime, timedelta
import jwt
import secrets
import base64
from io import BytesIO
from typing import Any, Optional

pwd_context = CryptContext(schemes=['bcrypt'], deprecated='auto')


class AuthService(AdminService):
    """
    认证相关服务类（登录、登出、权限验证等）
    """
    
    def __init__(self, db: AsyncSession):
        super().__init__(db)
        self.user_dal = self.get_dal(AdminUser)
        self.role_dal = self.get_dal(AdminRole)
        self.dept_dal = self.get_dal(AdminDept)
        self.menu_dal = self.get_dal(AdminMenu)
        self.login_record_dal = self.get_dal(AdminLoginRecord)
        
        # JWT配置
        self.secret_key = "your-secret-key"  # 应该从配置文件读取
        self.algorithm = "HS256"
        self.access_token_expire_minutes = 30
        self.refresh_token_expire_days = 7
    
    async def login(self, data: LoginRequest, request: Request) -> dict:
        """
        用户登录
        """
        # 验证验证码（如果需要）
        if data.captcha_key and data.captcha:
            # TODO: 实现验证码验证逻辑
            pass
        
        # 查找用户（支持手机号或邮箱登录）
        user = None
        if "@" in data.username:
            # 邮箱登录
            user = await self.user_dal.get_data(email=data.username, v_return_none=True)
        else:
            # 手机号登录
            user = await self.user_dal.get_data(telephone=data.username, v_return_none=True)
        
        if not user:
            raise CustomException("用户不存在")
        
        # 验证密码
        if not pwd_context.verify(data.password, user.password):
            # 记录登录失败
            await self._record_login(user.id, request, False, "密码错误")
            raise CustomException("密码错误")
        
        # 检查用户状态
        if not user.is_active:
            raise CustomException("用户已被禁用")
        
        # 生成访问令牌和刷新令牌
        access_token = self._create_access_token(user.id)
        refresh_token = self._create_refresh_token(user.id)
        
        # 更新用户最后登录信息
        await self.user_dal.put_data(user.id, {
            "last_login": datetime.now(),
            "last_ip": self._get_client_ip(request)
        })
        
        # 记录登录成功
        await self._record_login(user.id, request, True)
        
        # 获取用户完整信息
        user_info = await self._get_user_profile(user.id)
        
        return SuccessResponse(
            data=LoginResponse(
                access_token=access_token,
                refresh_token=refresh_token,
                expires_in=self.access_token_expire_minutes * 60,
                user_info=user_info
            ),
            msg="登录成功"
        )
    
    async def logout(self, request: Request) -> dict:
        """
        用户登出
        """
        # TODO: 实现令牌黑名单机制
        # 从请求头获取令牌并加入黑名单
        
        return SuccessResponse(
            data=LogoutResponse(),
            msg="登出成功"
        )
    
    async def refresh_token(self, data: RefreshTokenRequest) -> dict:
        """
        刷新访问令牌
        """
        try:
            # 验证刷新令牌
            payload = jwt.decode(data.refresh_token, self.secret_key, algorithms=[self.algorithm])
            user_id = payload.get("user_id")
            token_type = payload.get("type")
            
            if token_type != "refresh":
                raise CustomException("无效的刷新令牌")
            
            # 检查用户是否存在且活跃
            user = await self.user_dal.get_data(user_id)
            if not user or not user.is_active:
                raise CustomException("用户不存在或已被禁用")
            
            # 生成新的访问令牌
            access_token = self._create_access_token(user_id)
            
            return SuccessResponse(
                data=RefreshTokenResponse(
                    access_token=access_token,
                    expires_in=self.access_token_expire_minutes * 60
                ),
                msg="令牌刷新成功"
            )
            
        except jwt.ExpiredSignatureError:
            raise CustomException("刷新令牌已过期")
        except jwt.InvalidTokenError:
            raise CustomException("无效的刷新令牌")
    
    async def get_profile(self, user_id: int = None) -> dict:
        """
        获取当前用户信息
        """
        # TODO: 从当前认证上下文获取user_id
        if not user_id:
            raise CustomException("未认证")
        
        user_info = await self._get_user_profile(user_id)
        
        return SuccessResponse(data=user_info, msg="获取用户信息成功")
    
    async def update_profile(self, data: UserProfileUpdate, user_id: int = None) -> dict:
        """
        更新当前用户信息
        """
        # TODO: 从当前认证上下文获取user_id
        if not user_id:
            raise CustomException("未认证")
        
        # 检查邮箱是否被其他用户使用
        if data.email:
            existing_user = await self.user_dal.get_data(email=data.email, v_return_none=True)
            if existing_user and existing_user.id != user_id:
                raise CustomException("邮箱已被其他用户使用")
        
        # 更新用户信息
        update_data = data.model_dump(exclude_unset=True)
        await self.user_dal.put_data(user_id, update_data)
        
        # 获取更新后的用户信息
        user_info = await self._get_user_profile(user_id)
        
        return SuccessResponse(data=user_info, msg="更新用户信息成功")
    
    async def change_password(self, data: ChangePasswordRequest, user_id: int = None) -> dict:
        """
        修改当前用户密码
        """
        # TODO: 从当前认证上下文获取user_id
        if not user_id:
            raise CustomException("未认证")
        
        # 获取用户信息
        user = await self.user_dal.get_data(user_id)
        if not user:
            raise CustomException("用户不存在")
        
        # 验证旧密码
        if not pwd_context.verify(data.old_password, user.password):
            raise CustomException("旧密码错误")
        
        # 加密新密码
        hashed_password = pwd_context.hash(data.new_password)
        
        # 更新密码
        await self.user_dal.put_data(user_id, {"password": hashed_password})
        
        return SuccessResponse(msg="密码修改成功")
    
    async def get_permissions(self, user_id: int = None) -> dict:
        """
        获取当前用户权限
        """
        # TODO: 从当前认证上下文获取user_id
        if not user_id:
            raise CustomException("未认证")
        
        # 获取用户角色和权限
        user = await self.user_dal.get_data(user_id)
        if not user:
            raise CustomException("用户不存在")
        
        permissions = []
        # TODO: 实现权限获取逻辑
        
        return SuccessResponse(data=permissions, msg="获取用户权限成功")
    
    async def get_menus(self, user_id: int = None) -> dict:
        """
        获取当前用户菜单
        """
        # TODO: 从当前认证上下文获取user_id
        if not user_id:
            raise CustomException("未认证")
        
        # 获取用户菜单
        user = await self.user_dal.get_data(user_id)
        if not user:
            raise CustomException("用户不存在")
        
        menus = []
        # TODO: 实现菜单获取逻辑
        
        return SuccessResponse(data=menus, msg="获取用户菜单成功")
    
    async def verify_token(self, request: Request) -> dict:
        """
        验证访问令牌
        """
        try:
            # 从请求头获取令牌
            authorization = request.headers.get("Authorization")
            if not authorization or not authorization.startswith("Bearer "):
                raise CustomException("缺少访问令牌")
            
            token = authorization.split(" ")[1]
            
            # 验证令牌
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            user_id = payload.get("user_id")
            token_type = payload.get("type")
            
            if token_type != "access":
                raise CustomException("无效的访问令牌")
            
            # 检查用户是否存在且活跃
            user = await self.user_dal.get_data(user_id)
            if not user or not user.is_active:
                raise CustomException("用户不存在或已被禁用")
            
            return SuccessResponse(
                data=TokenVerifyResponse(
                    valid=True,
                    user_id=user.id,
                    username=user.name
                ),
                msg="令牌验证成功"
            )
            
        except jwt.ExpiredSignatureError:
            raise CustomException("访问令牌已过期")
        except jwt.InvalidTokenError:
            raise CustomException("无效的访问令牌")
    
    def _create_access_token(self, user_id: int) -> str:
        """
        创建访问令牌
        """
        expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        payload = {
            "user_id": user_id,
            "type": "access",
            "exp": expire
        }
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
    
    def _create_refresh_token(self, user_id: int) -> str:
        """
        创建刷新令牌
        """
        expire = datetime.utcnow() + timedelta(days=self.refresh_token_expire_days)
        payload = {
            "user_id": user_id,
            "type": "refresh",
            "exp": expire
        }
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
    
    def _get_client_ip(self, request: Request) -> str:
        """
        获取客户端IP地址
        """
        forwarded = request.headers.get("X-Forwarded-For")
        if forwarded:
            return forwarded.split(",")[0].strip()
        return request.client.host if request.client else "unknown"
    
    async def _record_login(self, user_id: int, request: Request, success: bool, message: str = None) -> None:
        """
        记录登录日志
        """
        # TODO: 实现登录记录逻辑
        pass
    
    async def _get_user_profile(self, user_id: int) -> UserProfileOut:
        """
        获取用户完整信息
        """
        user = await self.user_dal.get_data(user_id, v_schema=UserProfileOut)
        return user
