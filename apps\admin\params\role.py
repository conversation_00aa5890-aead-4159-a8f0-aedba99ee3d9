#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/21
# @File           : role.py
# @IDE            : PyCharm
# @desc           : 角色管理查询参数

from apps.admin.depts import QueryParams
from typing import Optional


class RoleParams(QueryParams):
    """
    角色查询参数
    """

    def __init__(
        self,
        name: Optional[str] = None,
        role_key: Optional[str] = None,
        disabled: Optional[bool] = None,
        is_admin: Optional[bool] = None,
        create_datetime_start: Optional[str] = None,
        create_datetime_end: Optional[str] = None,
        params=None
    ):
        super().__init__(params)
        self.name = name
        self.role_key = role_key
        self.disabled = disabled
        self.is_admin = is_admin
        self.create_datetime_start = create_datetime_start
        self.create_datetime_end = create_datetime_end
