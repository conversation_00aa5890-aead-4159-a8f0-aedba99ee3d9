# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/21
# @File           : router.py
# @IDE            : PyCharm
# @desc           : 应用路由配置文件

# 导入各个模块的API路由
from apps.admin.apis.auth import app as admin_auth_app
from apps.admin.apis.user import app as admin_user_app
from apps.admin.apis.role import app as admin_role_app
from apps.admin.apis.menu import app as admin_menu_app
from apps.admin.apis.dept import app as admin_dept_app
from apps.admin.apis.system import app as admin_system_app
from apps.admin.apis.task import app as admin_task_app
from apps.admin.apis.record import app as admin_record_app
from apps.admin.apis.help import app as admin_help_app
from apps.admin.apis.resource import app as admin_resource_app

# 引入应用中的路由
# 保持原有的接口路径不变，确保前端调用不受影响
urlpatterns = [
    {"ApiRouter": admin_auth_app, "prefix": "/admin/auth", "tags": ["认证管理"]},
    {"ApiRouter": admin_user_app, "prefix": "/admin/auth", "tags": ["用户管理"]},
    {"ApiRouter": admin_role_app, "prefix": "/admin/auth", "tags": ["角色管理"]},
    {"ApiRouter": admin_menu_app, "prefix": "/admin/auth", "tags": ["菜单管理"]},
    {"ApiRouter": admin_dept_app, "prefix": "/admin/auth", "tags": ["部门管理"]},
    {"ApiRouter": admin_system_app, "prefix": "/admin/system", "tags": ["系统管理"]},
    {"ApiRouter": admin_task_app, "prefix": "/admin/system", "tags": ["任务管理"]},
    {"ApiRouter": admin_record_app, "prefix": "/admin/record", "tags": ["记录管理"]},
    {"ApiRouter": admin_help_app, "prefix": "/admin/help", "tags": ["帮助中心管理"]},
    {"ApiRouter": admin_resource_app, "prefix": "/admin/resource", "tags": ["资源管理"]},
]
