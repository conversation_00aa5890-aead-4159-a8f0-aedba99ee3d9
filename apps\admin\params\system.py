#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/19
# @File           : system.py
# @IDE            : PyCharm
# @desc           : 系统管理相关查询参数

from apps.admin.depts import QueryParams
from typing import Optional


class DictTypeParams(QueryParams):
    """
    字典类型查询参数
    """
    
    def __init__(
        self,
        dict_name: Optional[str] = None,
        dict_type: Optional[str] = None,
        disabled: Optional[bool] = None,
        params=None
    ):
        super().__init__(params)
        self.dict_name = dict_name
        self.dict_type = dict_type
        self.disabled = disabled


class DictDetailsParams(QueryParams):
    """
    字典详情查询参数
    """
    
    def __init__(
        self,
        label: Optional[str] = None,
        value: Optional[str] = None,
        disabled: Optional[bool] = None,
        is_default: Optional[bool] = None,
        dict_type_id: Optional[int] = None,
        params=None
    ):
        super().__init__(params)
        self.label = label
        self.value = value
        self.disabled = disabled
        self.is_default = is_default
        self.dict_type_id = dict_type_id


class SettingsParams(QueryParams):
    """
    系统配置查询参数
    """
    
    def __init__(
        self,
        config_label: Optional[str] = None,
        config_key: Optional[str] = None,
        disabled: Optional[bool] = None,
        tab_id: Optional[int] = None,
        params=None
    ):
        super().__init__(params)
        self.config_label = config_label
        self.config_key = config_key
        self.disabled = disabled
        self.tab_id = tab_id


class SettingsTabParams(QueryParams):
    """
    系统配置标签查询参数
    """
    
    def __init__(
        self,
        title: Optional[str] = None,
        classify: Optional[str] = None,
        tab_name: Optional[str] = None,
        hidden: Optional[bool] = None,
        disabled: Optional[bool] = None,
        params=None
    ):
        super().__init__(params)
        self.title = title
        self.classify = classify
        self.tab_name = tab_name
        self.hidden = hidden
        self.disabled = disabled


class TaskParams(QueryParams):
    """
    定时任务查询参数
    """
    
    def __init__(
        self,
        name: Optional[str] = None,
        group: Optional[str] = None,
        status: Optional[str] = None,
        params=None
    ):
        super().__init__(params)
        self.name = name
        self.group = group
        self.status = status


class TaskRecordParams(QueryParams):
    """
    定时任务记录查询参数
    """
    
    def __init__(
        self,
        task_id: Optional[str] = None,
        status: Optional[str] = None,
        start_time: Optional[str] = None,
        end_time: Optional[str] = None,
        params=None
    ):
        super().__init__(params)
        self.task_id = task_id
        self.status = status
        self.start_time = start_time
        self.end_time = end_time
