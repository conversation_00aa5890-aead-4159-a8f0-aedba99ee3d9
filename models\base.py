# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2021/10/18 22:19
# @File           : base.py
# @IDE            : PyCharm
# @desc           : 数据库公共 ORM 模型

from core.database import Base
from sqlalchemy import inspect


# 使用命令：alembic init alembic 初始化迁移数据库环境
# 这时会生成alembic文件夹 和 alembic.ini文件
class BaseModel(Base):
    """
    公共 ORM 模型基类

    这是一个抽象基类，不包含任何具体字段。
    所有继承此类的模型都需要独立实现所有字段，包括：
    - id: 主键ID
    - create_datetime: 创建时间
    - update_datetime: 更新时间
    - delete_datetime: 删除时间（软删除）
    - is_delete: 是否软删除

    这样设计的优势：
    1. 每个模型的表结构定义更加清晰和独立
    2. 便于理解每个表的完整字段结构
    3. 避免隐式继承带来的字段不明确问题
    4. 更好的代码可读性和维护性
    """
    __abstract__ = True

    @classmethod
    def get_column_attrs(cls) -> list:
        """
        获取模型中除 relationships 外的所有字段名称
        :return:
        """
        mapper = inspect(cls)

        # for attr_name, column_property in mapper.column_attrs.items():
        #     # 假设它是单列属性
        #     column = column_property.columns[0]
        #     # 访问各种属性
        #     print(f"属性: {attr_name}")
        #     print(f"类型: {column.type}")
        #     print(f"默认值: {column.default}")
        #     print(f"服务器默认值: {column.server_default}")

        return mapper.column_attrs.keys()

    @classmethod
    def get_attrs(cls) -> list:
        """
        获取模型所有字段名称
        :return:
        """
        mapper = inspect(cls)
        return mapper.attrs.keys()

    @classmethod
    def get_relationships_attrs(cls) -> list:
        """
        获取模型中 relationships 所有字段名称
        :return:
        """
        mapper = inspect(cls)
        return mapper.relationships.keys()
